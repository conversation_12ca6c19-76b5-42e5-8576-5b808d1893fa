<template>
  <div class="concept-card">
    <div class="card-header">
      <h2 class="card-title">{{ title }}</h2>
      <div class="card-actions">
        <button @click="toggleExpanded" class="expand-button">
          {{ isExpanded ? '收起' : '展开' }}
        </button>
      </div>
    </div>

    <div class="card-content" :class="{ expanded: isExpanded }">
      <div class="key-points">
        <h3>🎯 核心要点</h3>
        <ul class="points-list">
          <li v-for="(point, index) in keyPoints" :key="index" class="point-item">
            {{ point }}
          </li>
        </ul>
      </div>

      <div v-if="interactiveElements && interactiveElements.length > 0" class="interactive-section">
        <h3>🎮 互动元素</h3>
        <div class="interactive-buttons">
          <button
            v-for="element in interactiveElements"
            :key="element.type"
            @click="handleInteraction(element.type)"
            class="interactive-button"
          >
            {{ element.label }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface InteractiveElement {
  type: string
  label: string
}

interface ConceptData {
  title: string
  keyPoints: string[]
  interactiveElements?: InteractiveElement[]
}

const props = defineProps<{
  title: string
  conceptData: ConceptData
}>()

const emit = defineEmits<{
  interaction: [type: string]
}>()

const isExpanded = ref(true)

const keyPoints = computed(() => props.conceptData.keyPoints)
const interactiveElements = computed(() => props.conceptData.interactiveElements)

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const handleInteraction = (type: string) => {
  emit('interaction', type)
}
</script>

<style scoped>
.concept-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.concept-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.expand-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.expand-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.card-content {
  padding: 1.5rem;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card-content.expanded {
  max-height: 1000px;
  padding: 1.5rem;
}

.key-points h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
}

.points-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.point-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid #f0f0f0;
  color: #555;
  line-height: 1.6;
  position: relative;
  padding-left: 1.5rem;
}

.point-item:before {
  content: '•';
  color: #667eea;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.point-item:last-child {
  border-bottom: none;
}

.interactive-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e9ecef;
}

.interactive-section h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
}

.interactive-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.interactive-button {
  background: #f8f9fa;
  color: #333;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.interactive-button:hover {
  background: #e9ecef;
  border-color: #667eea;
  color: #667eea;
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .interactive-buttons {
    flex-direction: column;
  }

  .interactive-button {
    width: 100%;
  }
}
</style>
