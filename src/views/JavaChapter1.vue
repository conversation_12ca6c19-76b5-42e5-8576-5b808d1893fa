<template>
  <div class="java-chapter1">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <h1 class="chapter-title">第一章：Java 语言与平台基础</h1>
          <p class="chapter-subtitle">深入理解 Java 语言特性与平台演进</p>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
            <span class="progress-text">{{ progress }}% 完成</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: Java语言 vs Java平台 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="Java 语言 vs. Java 平台"
                :concept-data="javaLanguagePlatformData"
                @interaction="handleInteraction"
              >
                <JavaCompilationAnimation />
                <div class="deep-dive">
                  <h3>🔍 深度解读</h3>
                  <div class="explanation-tabs">
                    <div class="tab-buttons">
                      <button
                        v-for="(tab, index) in explanationTabs"
                        :key="index"
                        @click="activeTab = index"
                        :class="['tab-button', { active: activeTab === index }]"
                      >
                        {{ tab.title }}
                      </button>
                    </div>
                    <div class="tab-content">
                      <div
                        v-for="(tab, index) in explanationTabs"
                        :key="index"
                        v-show="activeTab === index"
                        class="tab-panel"
                      >
                        <h4>{{ tab.title }}</h4>
                        <div v-if="tab.type === 'definitions'" class="definition-cards">
                          <div
                            v-for="def in tab.content"
                            :key="def.title"
                            :class="['definition-card', def.type]"
                          >
                            <h5>{{ def.title }}</h5>
                            <p>{{ def.description }}</p>
                            <ul>
                              <li v-for="feature in def.features" :key="feature">{{ feature }}</li>
                            </ul>
                          </div>
                        </div>
                        <div v-else-if="tab.type === 'timeline'" class="timeline">
                          <div
                            v-for="item in tab.content"
                            :key="item.version"
                            :class="['timeline-item', { lts: item.isLTS }]"
                          >
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                              <h5>
                                {{ item.version }}
                                <span v-if="item.isLTS" class="lts-badge">LTS</span>
                              </h5>
                              <p class="timeline-date">{{ item.date }}</p>
                              <p>{{ item.description }}</p>
                              <ul v-if="item.features">
                                <li v-for="feature in item.features" :key="feature">
                                  {{ feature }}
                                </li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 新的Java发布模型 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="新的 Java 发布模型"
                :concept-data="releaseModelData"
                @interaction="handleInteraction"
              >
                <div class="release-timeline">
                  <h3>📅 Java 发布时间线</h3>
                  <div class="timeline-container">
                    <div
                      v-for="release in javaReleases"
                      :key="release.version"
                      :class="['timeline-item', { lts: release.isLTS }]"
                    >
                      <div class="timeline-marker"></div>
                      <div class="timeline-content">
                        <h4>
                          Java {{ release.version }}
                          <span v-if="release.isLTS" class="lts-badge">LTS</span>
                        </h4>
                        <p class="timeline-date">{{ release.date }}</p>
                        <p class="timeline-features">{{ release.features }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: var关键字 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="增强的类型推断 (var 关键字)"
                :concept-data="varKeywordData"
                @interaction="handleInteraction"
              >
                <CodePlayground
                  :examples="varExamples"
                  title="var 关键字实践"
                  @code-run="handleCodeRun"
                />
              </ExpandableSection>
            </section>

            <!-- Topic 4: 语言与平台演进机制 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="语言与平台的演进机制"
                :concept-data="evolutionMechanismData"
                @interaction="handleInteraction"
              >
                <div class="evolution-mechanisms">
                  <h3>🔧 演进机制详解</h3>
                  <div class="mechanisms-grid">
                    <div class="mechanism-card syntactic-sugar">
                      <div class="mechanism-header">
                        <span class="mechanism-icon">🍬</span>
                        <h4>语法糖 (Syntactic Sugar)</h4>
                        <span class="cost-badge low">成本最低</span>
                      </div>
                      <div class="mechanism-content">
                        <p><strong>定义：</strong>编译器层面的语法简化，不涉及JVM变更</p>
                        <p><strong>示例：</strong>try-with-resources, enhanced for loop</p>
                        <p><strong>特点：</strong>编译时"脱糖"，转换为基础语法</p>
                      </div>
                    </div>

                    <div class="mechanism-card jeps-jsrs">
                      <div class="mechanism-header">
                        <span class="mechanism-icon">📋</span>
                        <h4>JEPs & JSRs</h4>
                        <span class="cost-badge medium">成本中等</span>
                      </div>
                      <div class="mechanism-content">
                        <p><strong>JSR：</strong>Java规范请求，流程较重，适合成熟技术</p>
                        <p><strong>JEP：</strong>JDK增强提案，轻量级，快速推动变更</p>
                        <p><strong>关系：</strong>大JSR通常由多个小JEP组成</p>
                      </div>
                    </div>

                    <div class="mechanism-card preview-features">
                      <div class="mechanism-header">
                        <span class="mechanism-icon">🧪</span>
                        <h4>预览特性 (Preview)</h4>
                        <span class="cost-badge high">风险较高</span>
                      </div>
                      <div class="mechanism-content">
                        <p><strong>目的：</strong>收集社区反馈，验证设计</p>
                        <p><strong>使用：</strong>需要--enable-preview标志</p>
                        <p><strong>警告：</strong>绝不可用于生产环境！</p>
                      </div>
                    </div>

                    <div class="mechanism-card incubating-features">
                      <div class="mechanism-header">
                        <span class="mechanism-icon">🥚</span>
                        <h4>孵化特性 (Incubating)</h4>
                        <span class="cost-badge medium">成本中等</span>
                      </div>
                      <div class="mechanism-content">
                        <p><strong>范围：</strong>主要是新的API模块</p>
                        <p><strong>位置：</strong>jdk.incubator包中</p>
                        <p><strong>示例：</strong>HTTP/2客户端最初就是孵化特性</p>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 5: Java 11 重要变更 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ExpandableSection
                title="Java 11 的重要小型变更"
                :concept-data="java11ChangesData"
                @interaction="handleInteraction"
              >
                <div class="java11-features">
                  <h3>🚀 Java 11 核心新特性</h3>

                  <!-- 集合工厂 -->
                  <div class="feature-detail">
                    <div class="feature-header">
                      <span class="feature-icon">📦</span>
                      <h4>集合工厂 (JEP 213)</h4>
                      <span class="jep-badge">JEP 213</span>
                    </div>
                    <div class="feature-content">
                      <div class="feature-description">
                        <p>通过静态工厂方法创建不可变集合，大大简化了小型集合的创建过程。</p>
                      </div>
                      <CodePlayground
                        :examples="collectionFactoryExamples"
                        title="集合工厂实践"
                        @code-run="handleCodeRun"
                      />
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- 章节总结与思维导图 -->
            <section id="topic-5" class="topic-section chapter-summary" ref="topic5">
              <ExpandableSection
                title="📊 章节总结与知识体系图"
                :concept-data="chapterSummaryData"
                @interaction="handleInteraction"
              >
                <div class="summary-content">
                  <h3>🎯 本章核心收获</h3>
                  <div class="key-takeaways">
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🔄</span>
                      <div>
                        <h4>理解Java双重身份</h4>
                        <p>区分Java语言和Java平台，掌握编译运行机制</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">📅</span>
                      <div>
                        <h4>新发布模型</h4>
                        <p>6个月发布周期，LTS版本策略，预览特性机制</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🔍</span>
                      <div>
                        <h4>var类型推断</h4>
                        <p>局部变量类型推断，提升代码可读性和开发效率</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">⚡</span>
                      <div>
                        <h4>Java 11特性</h4>
                        <p>HTTP客户端、字符串增强、文件操作简化等实用改进</p>
                      </div>
                    </div>
                  </div>

                  <div class="mindmap-container">
                    <h3>🧠 现代Java知识体系图</h3>
                    <div class="mindmap-wrapper">
                      <div id="chapter1-mindmap" class="mermaid-diagram">
                        <pre class="mermaid">
mindmap
  root((现代Java))
    1. Java双重身份
      Java语言
        语法规则
        编程范式
        语言特性
      Java平台
        JVM虚拟机
        标准库API
        运行时环境
      编译运行
        javac编译器
        字节码生成
        JVM执行

    2. 新发布模型
      时间驱动
        6个月周期
        定期发布
        可预测性
      LTS版本
        Java 8
        Java 11
        Java 17
        Java 21
      特性管理
        预览特性
        实验性API
        孵化模块

    3. var类型推断
      局部变量
        类型推断
        编译时确定
        运行时类型
      使用场景
        复杂泛型
        匿名类型
        流式操作
      最佳实践
        保持可读性
        避免过度使用
        明确语义

    4. 演进机制
      JEP流程
        提案阶段
        实现阶段
        集成阶段
      特性分类
        语言特性
        平台特性
        工具改进
      向后兼容
        源码兼容
        二进制兼容
        行为兼容

    5. Java 11亮点
      HTTP客户端
        原生支持
        异步API
        HTTP/2支持
      字符串增强
        strip方法
        repeat方法
        lines方法
      文件操作
        readString
        writeString
        简化API
                        </pre>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import JavaCompilationAnimation from '@/components/JavaCompilationAnimation.vue'
import CodePlayground from '@/components/CodePlayground.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const activeTab = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: 'Java 语言 vs. Java 平台',
    description: '理解语言与平台的根本区别，掌握跨平台原理',
  },
  {
    title: '新的 Java 发布模型',
    description: '了解时间驱动的发布周期和LTS版本策略',
  },
  {
    title: '增强的类型推断 (var)',
    description: '掌握var关键字的使用和局限性',
  },
  {
    title: '语言与平台演进机制',
    description: '学习语法糖、JEPs、孵化和预览特性',
  },
  {
    title: 'Java 11 重要变更',
    description: '探索集合工厂、HTTP/2客户端等新特性',
  },
  {
    title: '章节总结',
    description: '知识体系图和核心收获总结',
  },
]

// 解释标签页
const explanationTabs = [
  { title: '概念定义', type: 'definitions', content: [] },
  { title: '发布历史', type: 'timeline', content: [] },
]

// 数据定义
const javaLanguagePlatformData = {
  keyPoints: [
    '语言：静态类型、面向对象的编程语言（.java文件）',
    '平台：提供运行环境的软件集合，核心是JVM（.class文件）',
    '契约：.class文件格式是语言和平台间的唯一桥梁',
    '执行：编译+解释+JIT的动态编译系统',
  ],
}

const releaseModelData = {
  keyPoints: [
    '特性发布：每6个月发布一个新版本',
    'LTS版本：每2-3年指定一个长期支持版本',
    '时间驱动：不再等待重大特性，按时间表发布',
    '主线开发：特性分支开发，主干定期发布',
  ],
}

const varKeywordData = {
  keyPoints: [
    '局部变量类型推断：编译器自动推断变量类型',
    '静态类型：var不是动态类型，编译时确定类型',
    '减少冗余：简化复杂泛型类型的声明',
    '局限性：仅限局部变量，需要初始化',
  ],
}

const evolutionMechanismData = {
  keyPoints: [
    '语法糖：编译器层面的简化，成本最低',
    'JEPs：轻量级增强提案，快速推动变更',
    'JSRs：重量级规范请求，适合成熟技术',
    '预览特性：收集反馈，验证设计',
    '孵化特性：新API的试验场',
  ],
}

const java11ChangesData = {
  keyPoints: [
    '集合工厂：简化不可变集合创建',
    'HTTP/2客户端：现代化网络编程',
    '单文件程序：简化脚本开发',
    '移除EE模块：精简核心JDK',
    'String新方法：增强字符串处理',
  ],
}

const chapterSummaryData = {
  keyPoints: [
    '掌握Java语言与平台的本质区别',
    '理解现代Java的发布模型和版本策略',
    '学会使用var关键字进行类型推断',
    '了解Java生态的演进机制和特性管理',
  ],
}

const javaReleases = [
  { version: '8', date: '2014年3月', features: 'Lambda表达式、Stream API', isLTS: true },
  { version: '11', date: '2018年9月', features: 'HTTP/2客户端、单文件程序', isLTS: true },
  { version: '17', date: '2021年9月', features: 'Sealed Classes正式版', isLTS: true },
]

const varExamples = [
  {
    title: '基本用法对比',
    code: `var message = "Hello, Java!";
System.out.println("消息: " + message);`,
    explanation: 'var关键字简化了变量声明',
  },
]

const collectionFactoryExamples = [
  {
    title: '创建不可变List',
    code: `List<String> list = List.of("apple", "banana", "cherry");
System.out.println("列表: " + list);`,
    explanation: 'List.of()创建不可变集合',
  },
]

// 方法
const scrollToTopic = (index: number) => {
  currentTopic.value = index
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('Interaction:', type)
}

const handleCodeRun = (data: any) => {
  console.log('Code run:', data)
}

// 初始化Mermaid
onMounted(async () => {
  try {
    const mermaid = await import('mermaid')
    mermaid.default.initialize({
      startOnLoad: true,
      theme: 'default',
      securityLevel: 'loose',
      mindmap: {
        padding: 10,
        maxNodeWidth: 200,
      },
    })

    // 延迟渲染以确保DOM已加载
    setTimeout(() => {
      mermaid.default.run()
    }, 100)
  } catch (error) {
    console.warn('Mermaid not available:', error)
  }
})
</script>

<style scoped>
/* 基础样式 */
.java-chapter1 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 0;
  text-align: center;
}

.chapter-title {
  font-size: 2.5rem;
  margin: 0 0 1rem 0;
  font-weight: 700;
}

.chapter-subtitle {
  font-size: 1.2rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  height: 8px;
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.progress-fill {
  background: white;
  height: 100%;
  border-radius: 25px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -30px;
  right: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

.sidebar {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.outline {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.outline h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
}

.outline-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.5rem;
}

.outline-item:hover {
  background: #f8f9fa;
}

.outline-item.active {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.outline-number {
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.outline-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.outline-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tool-button {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.tool-button:hover {
  background: #f8f9fa;
  border-color: #667eea;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.topic-section {
  margin-bottom: 2rem;
}

.topic-section:last-child {
  margin-bottom: 0;
}

/* 深度解读样式 */
.deep-dive {
  margin-top: 2rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.explanation-tabs {
  margin-top: 1.5rem;
}

.tab-buttons {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tab-button {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.tab-button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.tab-panel h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
}

/* 发布时间线样式 */
.release-timeline {
  padding: 2rem;
}

.timeline-container {
  position: relative;
  padding-left: 2rem;
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e9ecef;
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 2rem;
}

.timeline-marker {
  position: absolute;
  left: -2rem;
  top: 0.5rem;
  width: 12px;
  height: 12px;
  background: #667eea;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-item.lts .timeline-marker {
  background: #4caf50;
  width: 16px;
  height: 16px;
  left: -2.2rem;
  top: 0.3rem;
}

.timeline-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.timeline-date {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
}

.timeline-features {
  color: #555;
  margin: 0;
}

.lts-badge {
  background: #4caf50;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* 演进机制样式 */
.evolution-mechanisms {
  padding: 2rem;
}

.mechanisms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.mechanism-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
}

.mechanism-card.syntactic-sugar {
  border-left-color: #4caf50;
}

.mechanism-card.jeps-jsrs {
  border-left-color: #2196f3;
}

.mechanism-card.preview-features {
  border-left-color: #ff9800;
}

.mechanism-card.incubating-features {
  border-left-color: #9c27b0;
}

.mechanism-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.mechanism-icon {
  font-size: 1.5rem;
}

.mechanism-header h4 {
  margin: 0;
  flex: 1;
  color: #333;
}

.cost-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.cost-badge.low {
  background: #e8f5e8;
  color: #4caf50;
}

.cost-badge.medium {
  background: #e3f2fd;
  color: #2196f3;
}

.cost-badge.high {
  background: #fff3e0;
  color: #ff9800;
}

.mechanism-content p {
  margin: 0.5rem 0;
  color: #666;
  line-height: 1.6;
}

/* Java 11 特性样式 */
.java11-features {
  padding: 2rem;
}

.feature-detail {
  margin-bottom: 3rem;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.feature-icon {
  font-size: 2rem;
}

.feature-header h4 {
  margin: 0;
  flex: 1;
  font-size: 1.3rem;
}

.jep-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.feature-content {
  padding: 2rem;
}

.feature-description p {
  color: #666;
  line-height: 1.6;
  font-size: 1.1rem;
  margin: 0 0 2rem 0;
}

@media (max-width: 768px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .chapter-title {
    font-size: 2rem;
  }

  .mechanisms-grid {
    grid-template-columns: 1fr;
  }
}

/* 章节总结样式 */
.chapter-summary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin: 2rem 0;
}

.summary-content {
  padding: 2rem;
}

.key-takeaways {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.takeaway-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.takeaway-item:hover {
  transform: translateY(-2px);
}

.takeaway-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.takeaway-item h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.takeaway-item p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

/* 思维导图样式 */
.mindmap-container {
  margin-top: 3rem;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.mindmap-container h3 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
}

.mindmap-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.mermaid-diagram {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mermaid-diagram pre {
  display: none; /* 隐藏原始代码，只显示渲染后的图表 */
}

/* Mermaid图表样式覆盖 */
.mindmap-wrapper .mermaid {
  max-width: 100%;
  height: auto;
}

.mindmap-wrapper svg {
  max-width: 100%;
  height: auto;
}

@media (max-width: 768px) {
  .key-takeaways {
    grid-template-columns: 1fr;
  }

  .mindmap-wrapper {
    min-height: 400px;
  }

  .takeaway-item {
    flex-direction: column;
    text-align: center;
  }

  .takeaway-icon {
    align-self: center;
  }
}
</style>
