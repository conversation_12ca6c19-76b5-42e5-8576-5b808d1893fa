# Cursor AI Coding Rules for This Vue 3 Project

## 1. 核心技术栈 (Core Tech Stack)

- **框架 (Framework):** Vue 3
- **语言 (Language):** TypeScript
- **状态管理 (State Management):** Pinia
- **构建工具 (Build Tool):** Vite
- **样式 (Styling):** CSS/SCSS with `<style scoped>`

---

## 2. Vue 组件编写规范 (Vue Component Authoring Rules)

### 2.1. 必须使用 `<script setup>`
所有新组件 **必须** 使用 `<script setup>` 语法糖。这是最简洁、最高效的组合式 API 写法。**禁止** 使用 Options API (`data()`, `methods`, `computed: {}` 等)。

```vue
<script setup lang="ts">
import { ref } from 'vue';

const count = ref(0);
</script>

<script>
import { defineComponent } from 'vue';

export default defineComponent({
  data() {
    return {
      count: 0,
    };
  },
});
</script>
```


### 2.2. 组件文件和命名

- 组件文件名使用 **帕斯卡命名法 (PascalCase)**，例如 `MyComponent.vue`。

- 在模板中使用组件时，也使用 PascalCase，例如 `<MyComponent />`。

### 2.3. 响应式状态声明

- 对**原始类型** (string, number, boolean) 使用 `ref`。

- 对**对象和数组**使用 `reactive` 或 `ref`。优先使用 `ref` 以保持一致性，除非你有特别的理由使用 `reactive`。

```TypeScript
// ✓ GOOD
import { ref } from 'vue';
const name = ref('Alice');
const user = ref({ id: 1, name: 'Bob' });
const list = ref([1, 2, 3]);
```


### 2.4. 组件 Props

- **必须** 为 `props` 提供完整的 TypeScript 类型定义。

- **必须** 使用 `defineProps<T>()` 泛型语法。

- 对于非必需的 props，使用 `?` 标记并考虑使用 `withDefaults` 提供默认值。

```TypeScript
// ✓ GOOD
import { withDefaults } from 'vue';

interface Props {
  title: string;
  items: string[];
  isActive?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false,
});
```


### 2.5. 组件 Emits

- **必须** 为 `emits` 提供完整的 TypeScript 类型定义，以明确事件名称和载荷类型。

- **必须** 使用 `defineEmits<T>()` 泛型语法。

```TypeScript
// ✓ GOOD
const emit = defineEmits<{
  (e: 'change', id: number): void;
  (e: 'update', value: string): void;
}>();

function handleClick() {
  emit('change', 123);
}
```


### 2.6. 生命周期函数

- 直接从 `vue` 中导入并使用组合式 API 的生命周期钩子。

```TypeScript
// ✓ GOOD
import { onMounted, onUnmounted } from 'vue';

onMounted(() => {
  console.log('Component is mounted!');
});
```


---

## 3. 模块化与代码组织 (Modularization & Code Organization)

### 3.1. 目录结构

遵循标准的项目结构，将不同类型的文件归类：

- `src/components/`: 存放可复用的基础组件。

- `src/views/`: 存放页面级组件。

- `src/composables/`: 存放可复用的组合式函数 (e.g., `useUser.ts`)。

- `src/store/`: 存放 Pinia 的 store 模块 (e.g., `user.ts`)。

- `src/types/`: 存放全局 TypeScript 类型定义 (e.g., `api.ts`)。

- `src/api/`: 存放 API 请求函数。

### 3.2. 逻辑复用

- **优先使用组合式函数 (Composables)** 来封装和复用有状态的逻辑。

- Composable 文件名应以 `use` 开头，例如 `useDraggable.ts`。

### 3.3. 页面布局

- 页面（Views）应由更小的、高内聚的组件（Components）组合而成。

- 保持页面级组件的模板（template）简洁，主要负责布局和组件的拼装，将复杂的 UI 和逻辑拆分到子组件中。

---

## 4. TypeScript 使用规范 (TypeScript Usage Rules)

- **类型优先 (Type First):** 尽可能为所有变量、函数参数和返回值添加明确的类型。

- **使用 `interface` 或 `type`:** 使用 `interface` 定义对象结构，使用 `type` 定义联合类型、元组或其他复杂类型。

- **避免 `any`:** **严禁** 使用 `any` 类型，除非在无法避免的情况下。如果类型不确定，请使用 `unknown` 并进行类型守卫。

---

## 5. 状态管理 (State Management)

- **使用 Pinia:** 项目的全局状态管理 **必须** 使用 Pinia。

- **模块化 Store:** 每个 store 应该只关心一个领域的功能。例如，`user.ts` 只管理用户信息，`cart.ts` 只管理购物车信息。

- 在组件中，直接从 store 文件中导入并使用 store 实例。

```TypeScriptn
// In src/store/counter.ts
import { defineStore } from 'pinia';

export const useCounterStore = defineStore('counter', {
  state: () => ({ count: 0 }),
  actions: {
    increment() {
      this.count++;
    },
  },
});
```

## 6. 样式规范 (Styling Rules)

- **使用 `<style scoped>`:** 所有组件的样式 **必须** 写在 `<style scoped>` 块中，以避免全局样式污染。

- **CSS 变量:** 鼓励使用 CSS 自定义属性（变量）来定义主题颜色、间距等，以便于维护和主题切换。


## 7. 代码注释与可访问性 (Comments & Accessibility)

- **JSDoc 注释:** 为所有公共的 Composable 函数、组件的 Props 和复杂的业务逻辑函数添加 JSDoc 风格的注释。

- **可访问性 (a11y):** 编写语义化的 HTML（例如，正确使用 `button`, `nav`, `main` 等标签），并为所有可交互但无文本内容的元素（如图标按钮）提供 `aria-label`。


