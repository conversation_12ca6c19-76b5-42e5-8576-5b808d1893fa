/* 类加载展示样式 */
.class-loading-showcase {
  padding: 2rem;
}

.journey-overview {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.journey-card {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.journey-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.journey-icon {
  font-size: 2rem;
}

.journey-header h4 {
  margin: 0;
  color: #333;
}

.journey-arrow {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

.loading-phases {
  margin: 2rem 0;
}

.phases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.phase-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #2c3e50;
}

.phase-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.phase-number {
  width: 30px;
  height: 30px;
  background: #2c3e50;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.phase-header h5 {
  margin: 0;
  color: #333;
}

.phase-description {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.phase-details h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.9rem;
}

.phase-details ul {
  margin: 0;
  padding-left: 1.5rem;
}

.phase-details li {
  margin-bottom: 0.25rem;
  color: #555;
  font-size: 0.9rem;
}

.error-comparison {
  margin: 2rem 0;
}

.error-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.error-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.error-card.cnf {
  border-left: 4px solid #dc3545;
}

.error-card.ncd {
  border-left: 4px solid #ffc107;
}

.error-card h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.error-stage {
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: inline-block;
}

.error-card.cnf .error-stage {
  background: #f8d7da;
  color: #721c24;
}

.error-card.ncd .error-stage {
  background: #fff3cd;
  color: #856404;
}

.error-example {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.error-example strong {
  color: #333;
}

.error-example code {
  background: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  display: block;
  margin: 0.5rem 0;
}

.error-example span {
  color: #666;
  font-size: 0.9rem;
}

/* 类加载器展示样式 */
.classloaders-showcase {
  padding: 2rem;
}

.delegation-model {
  margin: 2rem 0;
}

.model-explanation {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.explanation-text {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.explanation-text p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.delegation-hierarchy {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.loader-level {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loader-box {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  min-width: 250px;
}

.loader-level.bootstrap .loader-box {
  border-left: 4px solid #6f42c1;
}

.loader-level.platform .loader-box {
  border-left: 4px solid #20c997;
}

.loader-level.application .loader-box {
  border-left: 4px solid #fd7e14;
}

.loader-level.custom .loader-box {
  border-left: 4px solid #e83e8c;
}

.loader-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.loader-info h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.9rem;
}

.loader-info p {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.8rem;
  line-height: 1.4;
}

.loader-scope {
  background: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  color: #495057;
}

.delegation-arrow {
  font-size: 1.5rem;
  color: #667eea;
  margin: 0.5rem 0;
  text-align: center;
}

.isolation-demo {
  margin: 2rem 0;
}

.isolation-explanation {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.isolation-principle,
.isolation-applications {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.isolation-principle h5,
.isolation-applications h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.isolation-code {
  margin-top: 1rem;
}

.code-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-width: 100%;
  box-sizing: border-box;
}

.application-examples {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.app-example {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.app-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.app-example h6 {
  margin: 0 0 0.25rem 0;
  color: #333;
  font-size: 0.9rem;
}

.app-example p {
  margin: 0;
  color: #666;
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Class文件展示样式 */
.classfile-showcase {
  padding: 2rem;
}

.javap-introduction {
  margin: 2rem 0;
}

.tool-overview {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.tool-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.tool-icon {
  font-size: 2rem;
}

.tool-header h4 {
  margin: 0;
  color: #333;
}

.javap-options {
  margin: 2rem 0;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.option-flag {
  background: #2c3e50;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  font-size: 0.9rem;
}

.option-desc {
  color: #666;
  font-size: 0.9rem;
}

.type-descriptors {
  margin: 2rem 0;
}

.descriptors-explanation {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.descriptors-table {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.descriptor-category {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.descriptor-category h5 {
  margin: 0 0 1rem 0;
  color: #333;
  text-align: center;
}

.descriptor-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.descriptor-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.descriptor-item code {
  background: #2c3e50;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  min-width: 60px;
  text-align: center;
}

.descriptor-item span {
  color: #666;
  font-size: 0.9rem;
}

.constant-pool {
  margin: 2rem 0;
}

.pool-explanation {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.pool-concept,
.pool-structure {
  margin-bottom: 2rem;
}

.pool-concept h5,
.pool-structure h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.pool-example {
  margin: 1rem 0;
}

.pool-explanation-text {
  margin-top: 1rem;
}

.pool-explanation-text p {
  color: #666;
  line-height: 1.6;
}

/* 字节码展示样式 */
.bytecode-showcase {
  padding: 2rem;
}

.stack-architecture {
  margin: 2rem 0;
}

.architecture-explanation {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.arch-concept {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.arch-concept h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.arch-concept p {
  color: #666;
  line-height: 1.6;
}

.stack-demo {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stack-demo h5 {
  margin: 0 0 1rem 0;
  color: #333;
  text-align: center;
}

.execution-steps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.step {
  display: grid;
  grid-template-columns: 100px 120px 1fr;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.instruction {
  background: #2c3e50;
  color: white;
  padding: 0.5rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  text-align: center;
  font-weight: 600;
}

.stack-state {
  background: #667eea;
  color: white;
  padding: 0.5rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  text-align: center;
  font-weight: 600;
}

.description {
  color: #666;
  font-size: 0.9rem;
}

.step-arrow {
  text-align: center;
  color: #667eea;
  font-size: 1.5rem;
  font-weight: bold;
}

.instruction-categories {
  margin: 2rem 0;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.category-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.category-card:hover {
  transform: translateY(-2px);
}

.category-card.load-store {
  border-left: 4px solid #28a745;
}

.category-card.arithmetic {
  border-left: 4px solid #007bff;
}

.category-card.control {
  border-left: 4px solid #ffc107;
}

.category-card.method-call {
  border-left: 4px solid #dc3545;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.category-icon {
  font-size: 1.5rem;
}

.category-header h5 {
  margin: 0;
  color: #333;
}

.category-content p {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.instruction-examples {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.instruction-examples code {
  background: #2c3e50;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.instruction-examples span {
  color: #666;
  font-size: 0.8rem;
  margin-left: 0.5rem;
}

.invoke-dynamic {
  margin: 2rem 0;
}

.dynamic-explanation {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.dynamic-concept {
  margin-bottom: 2rem;
}

.dynamic-concept p {
  color: #666;
  line-height: 1.6;
}

.dynamic-applications h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.application-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.app-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.app-item .app-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.app-item h6 {
  margin: 0 0 0.25rem 0;
  color: #333;
  font-size: 0.9rem;
}

.app-item p {
  margin: 0;
  color: #666;
  font-size: 0.8rem;
  line-height: 1.4;
}

/* 反射展示样式 */
.reflection-showcase {
  padding: 2rem;
}

.reflection-concept {
  margin: 2rem 0;
}

.concept-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.concept-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.concept-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.concept-icon {
  font-size: 1.5rem;
}

.concept-header h4 {
  margin: 0;
  color: #333;
}

.concept-card p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.reflection-api {
  margin: 2rem 0;
}

.api-flow {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1.5rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.api-step {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.step-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.step-number {
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.step-header h5 {
  margin: 0;
  color: #333;
  font-size: 1rem;
}

.code-examples {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.code-examples code {
  background: #2c3e50;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.7rem;
}

.metadata-types,
.operation-types {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.metadata-item,
.operation-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  text-align: left;
}

.metadata-item code,
.operation-item code {
  background: #2c3e50;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.7rem;
}

.metadata-item span,
.operation-item span {
  color: #666;
  font-size: 0.8rem;
}

.flow-arrow {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

.reflection-internals {
  margin: 2rem 0;
}

.internals-explanation {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.inflation-mechanism h5 {
  margin: 0 0 1rem 0;
  color: #333;
}

.inflation-process {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-top: 1.5rem;
}

.inflation-stage {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stage-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.stage-content p {
  margin: 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.stage-content p:first-child {
  font-weight: 600;
  color: #333;
}

.inflation-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.inflation-arrow span {
  font-size: 0.8rem;
  color: #666;
  text-align: center;
}

.inflation-arrow .arrow {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

.reflection-tradeoffs {
  margin: 2rem 0;
}

.tradeoffs-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.tradeoff-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tradeoff-card.benefits {
  border-left: 4px solid #28a745;
}

.tradeoff-card.drawbacks {
  border-left: 4px solid #dc3545;
}

.tradeoff-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.tradeoff-icon {
  font-size: 1.5rem;
}

.tradeoff-header h5 {
  margin: 0;
  color: #333;
}

.tradeoff-card ul {
  margin: 0;
  padding-left: 1.5rem;
}

.tradeoff-card li {
  margin-bottom: 0.5rem;
  color: #666;
  line-height: 1.4;
}

.best-practices {
  background: #e3f2fd;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 2rem;
  border-left: 4px solid #2196f3;
}

.best-practices h5 {
  margin: 0 0 1rem 0;
  color: #1976d2;
}

.best-practices p {
  margin: 0;
  color: #333;
  line-height: 1.6;
}

/* 章节总结样式 */
.chapter-summary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin: 2rem 0;
}

.summary-content {
  padding: 2rem;
}

.key-takeaways {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.takeaway-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.takeaway-item:hover {
  transform: translateY(-2px);
}

.takeaway-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.takeaway-item h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.takeaway-item p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

/* 思维导图样式 */
.mindmap-container {
  margin-top: 3rem;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.mindmap-container h3 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
}

.mindmap-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.mermaid-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .journey-overview {
    flex-direction: column;
  }

  .journey-arrow {
    transform: rotate(90deg);
  }

  .phases-grid {
    grid-template-columns: 1fr;
  }

  .error-grid {
    grid-template-columns: 1fr;
  }

  .model-explanation {
    grid-template-columns: 1fr;
  }

  .isolation-explanation {
    grid-template-columns: 1fr;
  }

  .architecture-explanation {
    grid-template-columns: 1fr;
  }

  .categories-grid {
    grid-template-columns: 1fr;
  }

  .concept-overview {
    grid-template-columns: 1fr;
  }

  .api-flow {
    flex-direction: column;
  }

  .flow-arrow {
    transform: rotate(90deg);
  }

  .inflation-process {
    flex-direction: column;
  }

  .inflation-arrow .arrow {
    transform: rotate(90deg);
  }

  .tradeoffs-grid {
    grid-template-columns: 1fr;
  }

  .key-takeaways {
    grid-template-columns: 1fr;
  }

  .mindmap-wrapper {
    min-height: 400px;
  }
}

@media (max-width: 480px) {
  .step {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .code-block {
    font-size: 0.7rem;
    padding: 0.75rem;
  }

  .takeaway-item {
    flex-direction: column;
    text-align: center;
  }

  .takeaway-icon {
    align-self: center;
  }
}
