<template>
  <div class="about">
    <div class="container">
      <h1>关于本平台</h1>
      <div class="about-content">
        <div class="intro-section">
          <h2>📚 书籍可视化学习平台</h2>
          <p>
            这是一个专门为《The Well-Grounded Java Developer, Second Edition》设计的
            可视化可互动学习平台。我们的目标是通过现代化的Web技术，
            为Java学习者提供更加直观、有趣的学习体验。
          </p>
        </div>

        <div class="features-section">
          <h2>✨ 平台特色</h2>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">🎯</div>
              <h3>互动式学习</h3>
              <p>通过可点击的概念卡片、动画演示和代码实践，让学习更加生动有趣</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">📊</div>
              <h3>可视化图表</h3>
              <p>复杂的Java概念通过精美的图表和动画进行展示，帮助理解</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">💻</div>
              <h3>代码实践</h3>
              <p>内置代码编辑器，可以直接运行和测试Java代码示例</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">📝</div>
              <h3>学习笔记</h3>
              <p>支持在线记录学习笔记，跟踪学习进度</p>
            </div>
          </div>
        </div>

        <div class="tech-section">
          <h2>🛠️ 技术栈</h2>
          <div class="tech-list">
            <span class="tech-tag">Vue 3</span>
            <span class="tech-tag">TypeScript</span>
            <span class="tech-tag">Vite</span>
            <span class="tech-tag">CSS3 动画</span>
            <span class="tech-tag">响应式设计</span>
          </div>
        </div>

        <div class="book-section">
          <h2>📖 关于原书</h2>
          <div class="book-info">
            <div class="book-cover">
              <div class="book-placeholder">
                📚<br>
                The Well-Grounded<br>
                Java Developer<br>
                <small>Second Edition</small>
              </div>
            </div>
            <div class="book-details">
              <h3>《The Well-Grounded Java Developer, Second Edition》</h3>
              <p>
                这本书是Java开发者的必读经典，涵盖了从Java 8到Java 11及更高版本的
                现代Java开发技术。本书不仅介绍了语言特性，还深入探讨了JVM、
                性能优化、并发编程等高级主题。
              </p>
              <ul>
                <li>深入理解现代Java语言特性</li>
                <li>掌握JVM内部机制和性能调优</li>
                <li>学习并发编程和异步编程模式</li>
                <li>了解Java生态系统的最新发展</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这里可以添加任何需要的逻辑
</script>

<style scoped>
.about {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

h1 {
  text-align: center;
  font-size: 3rem;
  color: #333;
  margin-bottom: 3rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.about-content {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.intro-section,
.features-section,
.tech-section,
.book-section {
  margin-bottom: 3rem;
}

.intro-section h2,
.features-section h2,
.tech-section h2,
.book-section h2 {
  color: #667eea;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  border-bottom: 3px solid #667eea;
  padding-bottom: 0.5rem;
}

.intro-section p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

.tech-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.tech-tag {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.book-info {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 2rem;
  align-items: start;
}

.book-cover {
  display: flex;
  justify-content: center;
}

.book-placeholder {
  width: 150px;
  height: 200px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  font-weight: 600;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  transform: perspective(1000px) rotateY(-15deg);
}

.book-details h3 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.book-details p {
  color: #555;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.book-details ul {
  color: #555;
  line-height: 1.7;
}

.book-details li {
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 1.5rem;
}

.book-details li:before {
  content: '✓';
  color: #4CAF50;
  font-weight: bold;
  position: absolute;
  left: 0;
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  .about-content {
    padding: 2rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .book-info {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .tech-list {
    justify-content: center;
  }
}
</style>
