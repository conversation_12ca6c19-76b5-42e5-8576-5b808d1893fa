<template>
  <div class="java17-features-demo">
    <div class="demo-container">
      <h4>🚀 Java 17 特性互动演示</h4>

      <!-- 特性选择器 -->
      <div class="feature-selector">
        <button
          v-for="(feature, index) in features"
          :key="index"
          @click="selectFeature(index)"
          :class="['feature-btn', { active: selectedFeature === index }]"
        >
          {{ feature.name }}
        </button>
      </div>

      <!-- 演示区域 -->
      <div class="demo-area">
        <div class="demo-content">
          <!-- 文本块演示 -->
          <div v-if="selectedFeature === 0" class="text-blocks-demo">
            <h5>📝 文本块 (Text Blocks) 对比演示</h5>
            <div class="comparison-grid">
              <div class="old-way">
                <h6>传统方式 😰</h6>
                <pre
                  class="code-block old"
                ><code>String query = "SELECT \"ORDER_ID\", \"QUANTITY\", \"CURRENCY_PAIR\" FROM \"ORDERS\"\n" +
               "WHERE \"CLIENT_ID\" = ?\n" +
               "ORDER BY \"DATE_TIME\", \"STATUS\" LIMIT 100;";</code></pre>
                <div class="problems">
                  <span class="problem-tag">❌ 转义地狱</span>
                  <span class="problem-tag">❌ 字符串拼接</span>
                  <span class="problem-tag">❌ 可读性差</span>
                </div>
              </div>

              <div class="new-way">
                <h6>文本块方式 😍</h6>
                <pre class="code-block new"><code>String query = """
    SELECT "ORDER_ID", "QUANTITY", "CURRENCY_PAIR" FROM "ORDERS"
    WHERE "CLIENT_ID" = ?
    ORDER BY "DATE_TIME", "STATUS" LIMIT 100;
    """;</code></pre>
                <div class="benefits">
                  <span class="benefit-tag">✅ 所见即所得</span>
                  <span class="benefit-tag">✅ 智能缩进</span>
                  <span class="benefit-tag">✅ 极佳可读性</span>
                </div>
              </div>
            </div>

            <div class="interactive-demo">
              <h6>🎮 试试看：编辑文本块</h6>
              <textarea
                v-model="textBlockContent"
                class="text-block-editor"
                placeholder="在这里输入多行文本..."
              ></textarea>
              <div class="output">
                <strong>输出结果：</strong>
                <pre class="output-display">{{ textBlockContent }}</pre>
              </div>
            </div>
          </div>

          <!-- Switch表达式演示 -->
          <div v-if="selectedFeature === 1" class="switch-expressions-demo">
            <h5>🔀 Switch 表达式演示</h5>
            <div class="comparison-grid">
              <div class="old-way">
                <h6>传统 Switch 语句</h6>
                <pre class="code-block old"><code>String season;
switch (month) {
    case 12:
    case 1:
    case 2:
        season = "Winter";
        break;
    case 3:
    case 4:
    case 5:
        season = "Spring";
        break;
    // ... 更多case
    default:
        season = "Unknown";
        break;
}</code></pre>
                <div class="problems">
                  <span class="problem-tag">❌ 冗长的break</span>
                  <span class="problem-tag">❌ case穿透风险</span>
                  <span class="problem-tag">❌ 样板代码多</span>
                </div>
              </div>

              <div class="new-way">
                <h6>Switch 表达式</h6>
                <pre class="code-block new"><code>String season = switch (month) {
    case 12, 1, 2 -> "Winter";
    case 3, 4, 5 -> "Spring";
    case 6, 7, 8 -> "Summer";
    case 9, 10, 11 -> "Autumn";
    default -> "Unknown";
};</code></pre>
                <div class="benefits">
                  <span class="benefit-tag">✅ 无需break</span>
                  <span class="benefit-tag">✅ 返回值</span>
                  <span class="benefit-tag">✅ 详尽性检查</span>
                </div>
              </div>
            </div>

            <div class="interactive-demo">
              <h6>🎮 试试看：选择月份</h6>
              <select v-model="selectedMonth" class="month-selector">
                <option v-for="month in 12" :key="month" :value="month">{{ month }}月</option>
              </select>
              <div class="output">
                <strong>季节：</strong>
                <span class="season-result">{{ getSeason(selectedMonth) }}</span>
              </div>
            </div>
          </div>

          <!-- Records演示 -->
          <div v-if="selectedFeature === 2" class="records-demo">
            <h5>📋 记录类 (Records) 演示</h5>
            <div class="comparison-grid">
              <div class="old-way">
                <h6>传统POJO类</h6>
                <pre class="code-block old"><code>public final class PersonClassic {
    private final String name;
    private final int age;
    
    public PersonClassic(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    public String getName() { return name; }
    public int getAge() { return age; }
    
    @Override
    public boolean equals(Object o) {
        // 20+ 行样板代码...
    }
    
    @Override
    public int hashCode() {
        // 更多样板代码...
    }
    
    @Override
    public String toString() {
        // 还是样板代码...
    }
}</code></pre>
                <div class="problems">
                  <span class="problem-tag">❌ 50+ 行代码</span>
                  <span class="problem-tag">❌ 样板代码多</span>
                  <span class="problem-tag">❌ 易出错</span>
                </div>
              </div>

              <div class="new-way">
                <h6>Record 类</h6>
                <pre class="code-block new"><code>public record Person(String name, int age) {
    // 编译器自动生成：
    // - 构造函数
    // - name() 和 age() 访问器
    // - equals(), hashCode(), toString()
    
    // 可选：紧凑构造器用于验证
    public Person {
        if (age < 0) {
            throw new IllegalArgumentException("Age cannot be negative");
        }
    }
}</code></pre>
                <div class="benefits">
                  <span class="benefit-tag">✅ 仅10行代码</span>
                  <span class="benefit-tag">✅ 自动生成方法</span>
                  <span class="benefit-tag">✅ 不可变性</span>
                </div>
              </div>
            </div>

            <div class="interactive-demo">
              <h6>🎮 试试看：创建Person记录</h6>
              <div class="person-creator">
                <input v-model="personName" placeholder="姓名" class="person-input" />
                <input
                  v-model.number="personAge"
                  type="number"
                  placeholder="年龄"
                  class="person-input"
                />
                <button @click="createPerson" class="create-btn">创建Person</button>
              </div>
              <div v-if="createdPerson" class="output">
                <strong>创建的Person：</strong>
                <pre class="output-display">{{ createdPerson }}</pre>
              </div>
            </div>
          </div>

          <!-- Sealed Types演示 -->
          <div v-if="selectedFeature === 3" class="sealed-types-demo">
            <h5>🔒 密封类型 (Sealed Types) 演示</h5>
            <div class="sealed-hierarchy">
              <h6>密封类型层次结构</h6>
              <div class="hierarchy-diagram">
                <div class="sealed-class">
                  <span class="class-name">sealed interface Shape</span>
                  <span class="permits">permits Circle, Rectangle, Triangle</span>
                </div>
                <div class="implementations">
                  <div class="impl-class final">
                    <span class="class-name">record Circle(double radius)</span>
                    <span class="modifier">final (record隐式)</span>
                  </div>
                  <div class="impl-class final">
                    <span class="class-name">record Rectangle(double width, double height)</span>
                    <span class="modifier">final (record隐式)</span>
                  </div>
                  <div class="impl-class sealed">
                    <span class="class-name">sealed class Triangle</span>
                    <span class="modifier">permits EquilateralTriangle</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="switch-exhaustiveness">
              <h6>与Switch表达式的完美配合</h6>
              <pre class="code-block new"><code>public static double calculateArea(Shape shape) {
    return switch (shape) {
        case Circle(var radius) -> Math.PI * radius * radius;
        case Rectangle(var width, var height) -> width * height;
        case Triangle triangle -> calculateTriangleArea(triangle);
        // 无需default分支！编译器保证详尽性
    };
}</code></pre>
              <div class="benefits">
                <span class="benefit-tag">✅ 编译时安全</span>
                <span class="benefit-tag">✅ 无需default</span>
                <span class="benefit-tag">✅ 模式匹配</span>
              </div>
            </div>

            <div class="interactive-demo">
              <h6>🎮 试试看：选择形状计算面积</h6>
              <div class="shape-selector">
                <button
                  v-for="shape in shapes"
                  :key="shape.type"
                  @click="selectShape(shape)"
                  :class="['shape-btn', { active: selectedShape?.type === shape.type }]"
                >
                  {{ shape.name }}
                </button>
              </div>
              <div v-if="selectedShape" class="shape-params">
                <div v-for="(param, key) in selectedShape.params" :key="key" class="param-input">
                  <label>{{ key }}:</label>
                  <input
                    v-model.number="param.value"
                    type="number"
                    :placeholder="param.placeholder"
                    step="0.1"
                  />
                </div>
              </div>
              <div v-if="selectedShape" class="output">
                <strong>面积：</strong>
                <span class="area-result">{{ calculateShapeArea() }}</span>
              </div>
            </div>
          </div>

          <!-- 模式匹配演示 -->
          <div v-if="selectedFeature === 4" class="pattern-matching-demo">
            <h5>🎯 模式匹配演示</h5>
            <div class="comparison-grid">
              <div class="old-way">
                <h6>传统instanceof</h6>
                <pre class="code-block old"><code>if (obj instanceof String) {
    String s = (String) obj;
    System.out.println(s.toUpperCase());
} else if (obj instanceof Integer) {
    Integer i = (Integer) obj;
    System.out.println(i * 2);
}</code></pre>
                <div class="problems">
                  <span class="problem-tag">❌ 强制转换</span>
                  <span class="problem-tag">❌ 样板代码</span>
                  <span class="problem-tag">❌ 类型安全风险</span>
                </div>
              </div>

              <div class="new-way">
                <h6>模式匹配</h6>
                <pre class="code-block new"><code>// instanceof 模式匹配 (Java 16+)
if (obj instanceof String s) {
    System.out.println(s.toUpperCase());
} else if (obj instanceof Integer i) {
    System.out.println(i * 2);
}

// Switch 模式匹配 (预览特性)
return switch (obj) {
    case String s -> s.toUpperCase();
    case Integer i -> i * 2;
    case null -> "null value";
    default -> "unknown type";
};</code></pre>
                <div class="benefits">
                  <span class="benefit-tag">✅ 无需强转</span>
                  <span class="benefit-tag">✅ 类型安全</span>
                  <span class="benefit-tag">✅ 简洁表达</span>
                </div>
              </div>
            </div>

            <div class="interactive-demo">
              <h6>🎮 试试看：模式匹配不同类型</h6>
              <div class="type-selector">
                <button
                  v-for="type in dataTypes"
                  :key="type.name"
                  @click="selectDataType(type)"
                  :class="['type-btn', { active: selectedDataType?.name === type.name }]"
                >
                  {{ type.name }}
                </button>
              </div>
              <div v-if="selectedDataType" class="output">
                <strong>输入值：</strong>
                <span class="input-value">{{ selectedDataType.value }}</span>
                <br />
                <strong>模式匹配结果：</strong>
                <span class="pattern-result">{{ getPatternMatchResult() }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// 响应式数据
const selectedFeature = ref(0)
const textBlockContent = ref(`SELECT "ORDER_ID", "QUANTITY" 
FROM "ORDERS" 
WHERE "CLIENT_ID" = ?`)

const selectedMonth = ref(1)
const personName = ref('')
const personAge = ref(0)
const createdPerson = ref('')

const selectedShape = ref(null)
const selectedDataType = ref(null)

// 特性列表
const features = [
  { name: '文本块', icon: '📝' },
  { name: 'Switch表达式', icon: '🔀' },
  { name: '记录类', icon: '📋' },
  { name: '密封类型', icon: '🔒' },
  { name: '模式匹配', icon: '🎯' },
]

// 形状数据
const shapes = reactive([
  {
    type: 'circle',
    name: '圆形',
    params: {
      radius: { value: 5, placeholder: '半径' },
    },
  },
  {
    type: 'rectangle',
    name: '矩形',
    params: {
      width: { value: 4, placeholder: '宽度' },
      height: { value: 6, placeholder: '高度' },
    },
  },
  {
    type: 'triangle',
    name: '三角形',
    params: {
      base: { value: 8, placeholder: '底边' },
      height: { value: 5, placeholder: '高度' },
    },
  },
])

// 数据类型
const dataTypes = [
  { name: 'String', value: '"Hello World"' },
  { name: 'Integer', value: '42' },
  { name: 'Double', value: '3.14' },
  { name: 'Boolean', value: 'true' },
  { name: 'null', value: 'null' },
]

// 方法
const selectFeature = (index: number) => {
  selectedFeature.value = index
}

const getSeason = (month: number) => {
  switch (month) {
    case 12:
    case 1:
    case 2:
      return '冬季 ❄️'
    case 3:
    case 4:
    case 5:
      return '春季 🌸'
    case 6:
    case 7:
    case 8:
      return '夏季 ☀️'
    case 9:
    case 10:
    case 11:
      return '秋季 🍂'
    default:
      return '未知'
  }
}

const createPerson = () => {
  if (personName.value && personAge.value >= 0) {
    createdPerson.value = `Person[name=${personName.value}, age=${personAge.value}]`
  }
}

const selectShape = (shape: any) => {
  selectedShape.value = shape
}

const calculateShapeArea = () => {
  if (!selectedShape.value) return 0

  const shape = selectedShape.value
  switch (shape.type) {
    case 'circle':
      return (Math.PI * Math.pow(shape.params.radius.value, 2)).toFixed(2)
    case 'rectangle':
      return (shape.params.width.value * shape.params.height.value).toFixed(2)
    case 'triangle':
      return (0.5 * shape.params.base.value * shape.params.height.value).toFixed(2)
    default:
      return 0
  }
}

const selectDataType = (type: any) => {
  selectedDataType.value = type
}

const getPatternMatchResult = () => {
  if (!selectedDataType.value) return ''

  const type = selectedDataType.value
  switch (type.name) {
    case 'String':
      return `字符串转大写: ${type.value.toUpperCase()}`
    case 'Integer':
      return `整数乘以2: ${parseInt(type.value) * 2}`
    case 'Double':
      return `浮点数平方: ${Math.pow(parseFloat(type.value), 2).toFixed(2)}`
    case 'Boolean':
      return `布尔值取反: ${!JSON.parse(type.value)}`
    case 'null':
      return '处理null值: "空值处理完成"'
    default:
      return '未知类型'
  }
}
</script>

<style scoped>
.java17-features-demo {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.demo-container h4 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
}

.feature-selector {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.feature-btn {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #495057;
}

.feature-btn:hover {
  background: #f8f9fa;
  border-color: #667eea;
  transform: translateY(-2px);
}

.feature-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.demo-area {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.demo-content h5 {
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.old-way,
.new-way {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border: 2px solid;
}

.old-way {
  border-color: #dc3545;
}

.new-way {
  border-color: #28a745;
}

.old-way h6,
.new-way h6 {
  margin: 0 0 1rem 0;
  text-align: center;
}

.old-way h6 {
  color: #dc3545;
}

.new-way h6 {
  color: #28a745;
}

.code-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  overflow-x: auto;
  margin-bottom: 1rem;
}

.code-block.old {
  border-left: 4px solid #dc3545;
}

.code-block.new {
  border-left: 4px solid #28a745;
}

.problems,
.benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.problem-tag {
  background: #dc3545;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.benefit-tag {
  background: #28a745;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.interactive-demo {
  background: #e3f2fd;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 2rem;
}

.interactive-demo h6 {
  margin: 0 0 1rem 0;
  color: #1976d2;
}

.text-block-editor {
  width: 100%;
  min-height: 100px;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  resize: vertical;
}

.month-selector,
.person-input {
  padding: 0.5rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
}

.create-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.create-btn:hover {
  background: #5a6fd8;
}

.output {
  margin-top: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.output-display {
  background: #f8f9fa;
  padding: 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  margin-top: 0.5rem;
}

.season-result,
.area-result,
.pattern-result {
  font-weight: 600;
  color: #667eea;
  font-size: 1.1rem;
}

.input-value {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* Sealed Types 特殊样式 */
.sealed-hierarchy {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.hierarchy-diagram {
  text-align: center;
}

.sealed-class {
  background: #667eea;
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: inline-block;
}

.class-name {
  display: block;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.permits {
  display: block;
  font-size: 0.9rem;
  opacity: 0.9;
  margin-top: 0.5rem;
}

.implementations {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.impl-class {
  background: white;
  border: 2px solid;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  min-width: 200px;
}

.impl-class.final {
  border-color: #28a745;
}

.impl-class.sealed {
  border-color: #ffc107;
}

.modifier {
  display: block;
  font-size: 0.8rem;
  margin-top: 0.5rem;
  font-weight: 500;
}

.impl-class.final .modifier {
  color: #28a745;
}

.impl-class.sealed .modifier {
  color: #ffc107;
}

.switch-exhaustiveness {
  background: #e8f5e8;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.switch-exhaustiveness h6 {
  margin: 0 0 1rem 0;
  color: #28a745;
}

.person-creator {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.shape-selector,
.type-selector {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.shape-btn,
.type-btn {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.shape-btn:hover,
.type-btn:hover {
  border-color: #667eea;
}

.shape-btn.active,
.type-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.shape-params {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.param-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.param-input label {
  font-weight: 500;
  min-width: 60px;
}

.param-input input {
  padding: 0.5rem;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  width: 80px;
}

@media (max-width: 768px) {
  .comparison-grid {
    grid-template-columns: 1fr;
  }

  .feature-selector {
    flex-direction: column;
    align-items: center;
  }

  .implementations {
    flex-direction: column;
    align-items: center;
  }

  .person-creator,
  .shape-params {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
