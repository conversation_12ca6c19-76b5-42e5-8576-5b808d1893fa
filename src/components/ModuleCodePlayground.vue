<template>
  <div class="module-code-playground">
    <div class="playground-header">
      <h3>🧪 模块化代码实验室</h3>
      <p>在这里体验Java模块系统的各种特性，从模块声明到依赖管理！</p>
    </div>

    <div class="tabs-container">
      <div class="tabs">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="['tab-button', { active: activeTab === tab.id }]"
        >
          {{ tab.emoji }} {{ tab.title }}
        </button>
      </div>

      <div class="tab-content">
        <!-- 模块声明实验 -->
        <div v-if="activeTab === 'module-info'" class="experiment-section">
          <div class="experiment-header">
            <h4>📝 模块描述符 (module-info.java)</h4>
            <p>学习如何编写模块描述符，定义模块的边界和依赖关系</p>
          </div>

          <div class="code-editor-container">
            <div class="editor-section">
              <label>编辑 module-info.java：</label>
              <textarea
                v-model="moduleInfoCode"
                class="code-editor"
                placeholder="在这里编写module-info.java内容..."
                rows="12"
              ></textarea>
              <div class="editor-actions">
                <button @click="validateModuleInfo" class="validate-btn">🔍 验证语法</button>
                <button @click="explainModuleInfo" class="explain-btn">💡 解释代码</button>
                <button @click="resetModuleInfo" class="reset-btn">🔄 重置</button>
              </div>
            </div>

            <div class="output-section">
              <label>输出结果：</label>
              <div class="output-display" v-html="moduleInfoOutput"></div>
            </div>
          </div>
        </div>

        <!-- 依赖关系实验 -->
        <div v-if="activeTab === 'dependencies'" class="experiment-section">
          <div class="experiment-header">
            <h4>🔗 模块依赖关系</h4>
            <p>探索不同类型的模块依赖：requires、requires transitive、exports等</p>
          </div>

          <div class="dependency-demo">
            <div class="modules-grid">
              <div
                class="module-card"
                v-for="module in demoModules"
                :key="module.name"
                :class="['module-' + module.type]"
              >
                <h5>{{ module.name }}</h5>
                <div class="module-info">
                  <div class="exports" v-if="module.exports.length">
                    <strong>导出:</strong>
                    <ul>
                      <li v-for="exp in module.exports" :key="exp">{{ exp }}</li>
                    </ul>
                  </div>
                  <div class="requires" v-if="module.requires.length">
                    <strong>依赖:</strong>
                    <ul>
                      <li v-for="req in module.requires" :key="req">{{ req }}</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div class="dependency-controls">
              <button @click="showDependencyGraph" class="graph-btn">📊 显示依赖图</button>
              <button @click="checkCircularDeps" class="check-btn">🔍 检查循环依赖</button>
            </div>

            <div class="dependency-output" v-if="dependencyOutput">
              <h5>分析结果：</h5>
              <div v-html="dependencyOutput"></div>
            </div>
          </div>
        </div>

        <!-- 强封装实验 -->
        <div v-if="activeTab === 'encapsulation'" class="experiment-section">
          <div class="experiment-header">
            <h4>🔒 强封装机制</h4>
            <p>体验模块的强封装如何保护内部实现</p>
          </div>

          <div class="encapsulation-demo">
            <div class="scenario-selector">
              <label>选择访问场景：</label>
              <select v-model="selectedScenario" @change="updateEncapsulationResult">
                <option value="exported-public">访问已导出包的public类</option>
                <option value="internal-public">访问未导出包的public类</option>
                <option value="internal-package">访问包私有类</option>
                <option value="reflection-access">通过反射访问内部类</option>
              </select>
            </div>

            <div class="scenario-code">
              <div class="source-module">
                <h5>源模块 (library.module)</h5>
                <pre><code>{{ encapsulationScenarios[selectedScenario].sourceCode }}</code></pre>
              </div>

              <div class="client-module">
                <h5>客户端模块 (client.module)</h5>
                <pre><code>{{ encapsulationScenarios[selectedScenario].clientCode }}</code></pre>
              </div>
            </div>

            <div class="encapsulation-result" :class="encapsulationResult.type">
              <h5>编译结果：</h5>
              <p>
                <strong>{{ encapsulationResult.status }}</strong>
              </p>
              <p>{{ encapsulationResult.explanation }}</p>
            </div>
          </div>
        </div>

        <!-- jlink工具实验 -->
        <div v-if="activeTab === 'jlink'" class="experiment-section">
          <div class="experiment-header">
            <h4>⚡ jlink 自定义运行时</h4>
            <p>体验如何使用jlink工具创建精简的Java运行时镜像</p>
          </div>

          <div class="jlink-demo">
            <div class="jlink-config">
              <h5>配置jlink参数：</h5>
              <div class="config-row">
                <label>选择需要的模块：</label>
                <div class="module-checkboxes">
                  <label v-for="mod in availableModules" :key="mod.name">
                    <input type="checkbox" v-model="selectedModules" :value="mod.name" />
                    {{ mod.name }} <span class="module-size">({{ mod.size }})</span>
                  </label>
                </div>
              </div>

              <div class="config-row">
                <label>输出目录：</label>
                <input v-model="jlinkOutputDir" type="text" placeholder="my-custom-jre" />
              </div>

              <div class="config-row">
                <label>压缩级别：</label>
                <select v-model="compressionLevel">
                  <option value="0">0 - 无压缩</option>
                  <option value="1">1 - 常量字符串共享</option>
                  <option value="2">2 - ZIP压缩</option>
                </select>
              </div>
            </div>

            <div class="jlink-actions">
              <button @click="generateJlinkCommand" class="generate-btn">🔧 生成命令</button>
              <button @click="simulateJlink" class="simulate-btn">🚀 模拟执行</button>
            </div>

            <div class="jlink-output" v-if="jlinkCommand">
              <h5>生成的jlink命令：</h5>
              <pre class="command-output"><code>{{ jlinkCommand }}</code></pre>

              <div class="size-comparison" v-if="jlinkResult">
                <h5>大小对比：</h5>
                <div class="size-bars">
                  <div class="size-bar full-jre">
                    <span>完整JRE</span>
                    <div class="bar" style="width: 100%">{{ fullJreSize }}MB</div>
                  </div>
                  <div class="size-bar custom-jre">
                    <span>自定义运行时</span>
                    <div class="bar" :style="`width: ${jlinkResult.percentage}%`">
                      {{ jlinkResult.size }}MB
                    </div>
                  </div>
                </div>
                <p class="savings">
                  💰 节省了 {{ jlinkResult.savings }}MB ({{
                    (100 - jlinkResult.percentage).toFixed(1)
                  }}%)
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'

interface Tab {
  id: string
  title: string
  emoji: string
}

interface DemoModule {
  name: string
  type: 'platform' | 'application' | 'automatic'
  exports: string[]
  requires: string[]
}

const tabs: Tab[] = [
  { id: 'module-info', title: '模块声明', emoji: '📝' },
  { id: 'dependencies', title: '依赖关系', emoji: '🔗' },
  { id: 'encapsulation', title: '强封装', emoji: '🔒' },
  { id: 'jlink', title: 'jlink工具', emoji: '⚡' },
]

const activeTab = ref('module-info')

// 模块声明相关
const moduleInfoCode = ref(`module com.example.myapp {
    // 导出公共 API 包
    exports com.example.myapp.api;
    
    // 声明对其他模块的依赖
    requires java.base;
    requires java.logging;
    
    // 传递性依赖
    requires transitive java.sql;
    
    // 为框架开放反射访问
    opens com.example.myapp.internal to 
        com.fasterxml.jackson.databind;
    
    // 提供服务实现
    provides com.example.myapp.spi.Service with
        com.example.myapp.impl.ServiceImpl;
        
    // 使用服务
    uses com.example.myapp.spi.Service;
}`)

const moduleInfoOutput = ref('')

// 依赖关系相关
const demoModules = reactive<DemoModule[]>([
  {
    name: 'java.base',
    type: 'platform',
    exports: ['java.lang', 'java.util', 'java.io'],
    requires: [],
  },
  {
    name: 'java.sql',
    type: 'platform',
    exports: ['java.sql'],
    requires: ['java.base'],
  },
  {
    name: 'my.service',
    type: 'application',
    exports: ['com.mycompany.service.api'],
    requires: ['java.base', 'java.sql'],
  },
  {
    name: 'my.client',
    type: 'application',
    exports: ['com.mycompany.client'],
    requires: ['java.base', 'my.service'],
  },
])

const dependencyOutput = ref('')

// 强封装相关
const selectedScenario = ref('exported-public')

const encapsulationScenarios = {
  'exported-public': {
    sourceCode: `module library.module {
    exports com.library.api;
    // com.library.internal 未导出
}

package com.library.api;
public class PublicAPI {
    public void publicMethod() { }
}`,
    clientCode: `module client.module {
    requires library.module;
}

import com.library.api.PublicAPI;

public class Client {
    public void useAPI() {
        PublicAPI api = new PublicAPI(); // ✅ 成功
        api.publicMethod();
    }
}`,
  },
  'internal-public': {
    sourceCode: `module library.module {
    exports com.library.api;
    // com.library.internal 未导出
}

package com.library.internal;
public class InternalClass {
    public void internalMethod() { }
}`,
    clientCode: `module client.module {
    requires library.module;
}

import com.library.internal.InternalClass; // ❌ 编译错误

public class Client {
    public void useInternal() {
        InternalClass internal = new InternalClass();
    }
}`,
  },
  'internal-package': {
    sourceCode: `module library.module {
    exports com.library.api;
}

package com.library.internal;
class PackagePrivateClass {
    void packageMethod() { }
}`,
    clientCode: `module client.module {
    requires library.module;
}

// 无法导入包私有类
// import com.library.internal.PackagePrivateClass; // ❌ 编译错误

public class Client {
    // 无法访问
}`,
  },
  'reflection-access': {
    sourceCode: `module library.module {
    exports com.library.api;
    // 未opens com.library.internal
}

package com.library.internal;
public class InternalClass {
    private String secret = "hidden";
}`,
    clientCode: `module client.module {
    requires library.module;
}

import java.lang.reflect.Field;

public class Client {
    public void reflectiveAccess() {
        try {
            Class<?> clazz = Class.forName("com.library.internal.InternalClass");
            Field field = clazz.getDeclaredField("secret"); // ❌ 运行时异常
            field.setAccessible(true);
        } catch (Exception e) {
            // InaccessibleObjectException
        }
    }
}`,
  },
}

const encapsulationResult = ref({
  type: 'success',
  status: '编译成功',
  explanation: '可以正常访问已导出包中的public类',
})

// jlink相关
const availableModules = ref([
  { name: 'java.base', size: '15MB' },
  { name: 'java.logging', size: '0.2MB' },
  { name: 'java.sql', size: '2MB' },
  { name: 'java.xml', size: '8MB' },
  { name: 'java.desktop', size: '25MB' },
  { name: 'java.net.http', size: '0.5MB' },
  { name: 'jdk.crypto.ec', size: '0.8MB' },
])

const selectedModules = ref(['java.base', 'java.logging'])
const jlinkOutputDir = ref('my-custom-jre')
const compressionLevel = ref('1')
const jlinkCommand = ref('')
const jlinkResult = ref<any>(null)
const fullJreSize = 180

// 方法定义
const validateModuleInfo = () => {
  const code = moduleInfoCode.value.trim()

  if (!code) {
    moduleInfoOutput.value = '<span class="error">❌ 代码不能为空</span>'
    return
  }

  // 简单的语法检查
  const issues = []

  if (!code.includes('module ')) {
    issues.push('缺少module声明')
  }

  if (code.includes('exports') && !code.match(/exports\s+[\w.]+/)) {
    issues.push('exports语法可能有误')
  }

  if (code.includes('requires') && !code.match(/requires\s+[\w.]+/)) {
    issues.push('requires语法可能有误')
  }

  if (issues.length === 0) {
    moduleInfoOutput.value = `
      <span class="success">✅ 语法检查通过!</span>
      <br><br>
      <strong>检测到的特性:</strong>
      <ul>
        ${code.includes('exports') ? '<li>✓ 包导出声明</li>' : ''}
        ${code.includes('requires') ? '<li>✓ 模块依赖声明</li>' : ''}
        ${code.includes('transitive') ? '<li>✓ 传递性依赖</li>' : ''}
        ${code.includes('opens') ? '<li>✓ 反射访问开放</li>' : ''}
        ${code.includes('provides') ? '<li>✓ 服务提供者</li>' : ''}
        ${code.includes('uses') ? '<li>✓ 服务消费者</li>' : ''}
      </ul>
    `
  } else {
    moduleInfoOutput.value = `
      <span class="error">❌ 发现语法问题:</span>
      <ul>
        ${issues.map((issue) => `<li>${issue}</li>`).join('')}
      </ul>
    `
  }
}

const explainModuleInfo = () => {
  moduleInfoOutput.value = `
    <div class="explanation">
      <h4>📚 模块描述符详解:</h4>
      <ul>
        <li><strong>module:</strong> 声明模块名称，通常遵循包命名约定</li>
        <li><strong>exports:</strong> 导出包，使其public类型对其他模块可见</li>
        <li><strong>requires:</strong> 声明对其他模块的依赖</li>
        <li><strong>requires transitive:</strong> 传递性依赖，依赖我的模块自动获得此依赖</li>
        <li><strong>opens:</strong> 为反射访问开放包（深度反射）</li>
        <li><strong>provides...with:</strong> 提供服务实现</li>
        <li><strong>uses:</strong> 声明使用某个服务</li>
      </ul>
    </div>
  `
}

const resetModuleInfo = () => {
  moduleInfoCode.value = `module com.example.myapp {
    exports com.example.myapp.api;
    requires java.base;
    requires java.logging;
}`
  moduleInfoOutput.value = ''
}

const showDependencyGraph = () => {
  dependencyOutput.value = `
    <div class="dependency-graph">
      <h4>📊 模块依赖图:</h4>
      <pre>
java.base (平台模块)
    ↑
java.sql (平台模块)
    ↑  
my.service (应用模块)
    ↑
my.client (应用模块)
      </pre>
      <p><strong>分析结果:</strong> 依赖层次清晰，无循环依赖</p>
    </div>
  `
}

const checkCircularDeps = () => {
  dependencyOutput.value = `
    <div class="circular-check">
      <h4>🔍 循环依赖检查:</h4>
      <p class="success">✅ 未发现循环依赖</p>
      <p>所有模块的依赖关系都是单向的，形成了健康的依赖树结构。</p>
    </div>
  `
}

const updateEncapsulationResult = () => {
  const scenarios = {
    'exported-public': {
      type: 'success',
      status: '✅ 编译成功',
      explanation: '可以正常访问已导出包中的public类。这是模块间正常的API调用。',
    },
    'internal-public': {
      type: 'error',
      status: '❌ 编译失败',
      explanation: '无法访问未导出包中的类，即使它是public的。这就是强封装的威力！',
    },
    'internal-package': {
      type: 'error',
      status: '❌ 编译失败',
      explanation: '包私有类本来就不能跨包访问，在模块系统中更是如此。',
    },
    'reflection-access': {
      type: 'warning',
      status: '⚠️ 运行时异常',
      explanation:
        '编译通过，但运行时会抛出InaccessibleObjectException。需要在module-info中使用opens声明。',
    },
  }

  encapsulationResult.value = scenarios[selectedScenario.value as keyof typeof scenarios]
}

const generateJlinkCommand = () => {
  const modules = selectedModules.value.join(',')
  jlinkCommand.value = `jlink \\
  --module-path /path/to/jmods \\
  --add-modules ${modules} \\
  --output ${jlinkOutputDir.value} \\
  --compress=${compressionLevel.value} \\
  --no-header-files \\
  --no-man-pages`
}

const simulateJlink = () => {
  if (!jlinkCommand.value) {
    generateJlinkCommand()
  }

  // 模拟计算大小
  const moduleSizes = {
    'java.base': 15,
    'java.logging': 0.2,
    'java.sql': 2,
    'java.xml': 8,
    'java.desktop': 25,
    'java.net.http': 0.5,
    'jdk.crypto.ec': 0.8,
  }

  let totalSize = 0
  selectedModules.value.forEach((mod) => {
    totalSize += moduleSizes[mod as keyof typeof moduleSizes] || 1
  })

  // 应用压缩
  const compressionRatio = {
    '0': 1,
    '1': 0.85,
    '2': 0.7,
  }

  const finalSize =
    totalSize * compressionRatio[compressionLevel.value as keyof typeof compressionRatio]

  jlinkResult.value = {
    size: finalSize.toFixed(1),
    percentage: (finalSize / fullJreSize) * 100,
    savings: (fullJreSize - finalSize).toFixed(1),
  }
}

onMounted(() => {
  updateEncapsulationResult()
})
</script>

<style scoped>
.module-code-playground {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.playground-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
}

.playground-header h3 {
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.tabs-container {
  padding: 0;
}

.tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  overflow-x: auto;
}

.tab-button {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-size: 0.9rem;
}

.tab-button:hover {
  background: #e9ecef;
}

.tab-button.active {
  background: white;
  border-bottom: 3px solid #667eea;
  color: #667eea;
  font-weight: bold;
}

.tab-content {
  padding: 2rem;
}

.experiment-section {
  max-width: 100%;
}

.experiment-header {
  margin-bottom: 2rem;
  text-align: center;
}

.experiment-header h4 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.experiment-header p {
  color: #666;
  font-size: 0.95rem;
}

/* 代码编辑器样式 */
.code-editor-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.editor-section,
.output-section {
  display: flex;
  flex-direction: column;
}

.editor-section label,
.output-section label {
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #495057;
}

.code-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  resize: vertical;
  background: #f8f9fa;
}

.code-editor:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.editor-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.editor-actions button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.validate-btn {
  background: #28a745;
  color: white;
}
.explain-btn {
  background: #17a2b8;
  color: white;
}
.reset-btn {
  background: #ffc107;
  color: #212529;
}

.validate-btn:hover {
  background: #218838;
}
.explain-btn:hover {
  background: #138496;
}
.reset-btn:hover {
  background: #e0a800;
}

.output-display {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  min-height: 200px;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
}

/* 依赖关系样式 */
.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.module-card {
  padding: 1.5rem;
  border-radius: 8px;
  border: 2px solid;
}

.module-platform {
  border-color: #28a745;
  background: #f8fff9;
}
.module-application {
  border-color: #ffc107;
  background: #fffdf5;
}
.module-automatic {
  border-color: #6f42c1;
  background: #f8f7ff;
}

.module-card h5 {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.module-info .exports,
.module-info .requires {
  margin-bottom: 0.5rem;
}

.module-info ul {
  margin: 0.5rem 0 0 1rem;
  padding: 0;
}

.module-info li {
  font-size: 0.85rem;
  color: #666;
}

.dependency-controls {
  text-align: center;
  margin-bottom: 1.5rem;
}

.graph-btn,
.check-btn {
  padding: 0.75rem 1.5rem;
  margin: 0 0.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
}

.graph-btn {
  background: #667eea;
  color: white;
}
.check-btn {
  background: #28a745;
  color: white;
}

.dependency-output {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

/* 强封装样式 */
.scenario-selector {
  margin-bottom: 1.5rem;
}

.scenario-selector label {
  font-weight: bold;
  margin-right: 1rem;
}

.scenario-selector select {
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9rem;
}

.scenario-code {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.source-module,
.client-module {
  min-width: 0; /* 允许flex项目收缩 */
  overflow: hidden;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.source-module h5,
.client-module h5 {
  margin-bottom: 1rem;
  color: #495057;
}

.scenario-code pre {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  font-size: 0.8rem;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-width: 100%;
  box-sizing: border-box;
}

.scenario-code pre code {
  white-space: pre-wrap;
  word-break: break-word;
}

.encapsulation-result {
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid;
}

.encapsulation-result.success {
  background: #d4edda;
  border-color: #28a745;
}

.encapsulation-result.error {
  background: #f8d7da;
  border-color: #dc3545;
}

.encapsulation-result.warning {
  background: #fff3cd;
  border-color: #ffc107;
}

/* jlink样式 */
.jlink-config {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.config-row {
  margin-bottom: 1rem;
}

.config-row label {
  display: block;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.module-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.module-checkboxes label {
  display: flex;
  align-items: center;
  font-weight: normal;
  margin-bottom: 0;
}

.module-checkboxes input {
  margin-right: 0.5rem;
}

.module-size {
  color: #666;
  font-size: 0.8rem;
}

.config-row input,
.config-row select {
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 100%;
  max-width: 300px;
}

.jlink-actions {
  text-align: center;
  margin-bottom: 1.5rem;
}

.generate-btn,
.simulate-btn {
  padding: 0.75rem 1.5rem;
  margin: 0 0.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
}

.generate-btn {
  background: #17a2b8;
  color: white;
}
.simulate-btn {
  background: #28a745;
  color: white;
}

.command-output {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.85rem;
  overflow-x: auto;
}

.size-comparison {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.size-bars {
  margin: 1rem 0;
}

.size-bar {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.size-bar span {
  width: 120px;
  font-size: 0.9rem;
  font-weight: bold;
}

.size-bar .bar {
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
  color: white;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  text-align: center;
  min-width: 60px;
}

.size-bar.custom-jre .bar {
  background: linear-gradient(90deg, #51cf66, #40c057);
}

.savings {
  font-weight: bold;
  color: #28a745;
  font-size: 1.1rem;
  text-align: center;
  margin-top: 1rem;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .code-editor-container,
  .scenario-code {
    grid-template-columns: 1fr;
  }

  .tabs {
    flex-direction: column;
  }

  .tab-button {
    text-align: left;
  }

  .modules-grid {
    grid-template-columns: 1fr;
  }

  .module-checkboxes {
    grid-template-columns: 1fr;
  }
}

/* 输出内容的样式 */
.success {
  color: #28a745;
  font-weight: bold;
}
.error {
  color: #dc3545;
  font-weight: bold;
}
.warning {
  color: #ffc107;
  font-weight: bold;
}

.explanation {
  background: #e3f2fd;
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid #2196f3;
}

.explanation h4 {
  color: #1976d2;
  margin-bottom: 0.5rem;
}

.explanation ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.explanation li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.dependency-graph,
.circular-check {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.dependency-graph pre {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
  line-height: 1.6;
}
</style>
