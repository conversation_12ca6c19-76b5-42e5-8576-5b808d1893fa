<template>
  <section class="page-section">
    <h2 class="section-title">{{ title }}</h2>
    <div class="section-content">
      <slot></slot>
    </div>
  </section>
</template>

<script setup lang="ts">
defineProps<{
  title: string
}>()
</script>

<style scoped>
.page-section {
  margin-bottom: 40px;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-title {
  font-size: 1.8em;
  color: #fff;
  background: linear-gradient(to right, #007acc, #005a9e);
  padding: 15px 25px;
  margin: 0;
  font-weight: 600;
}

.section-content {
  padding: 25px;
}
</style>
