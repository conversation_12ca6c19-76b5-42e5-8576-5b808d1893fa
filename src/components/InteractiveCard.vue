<template>
  <div class="card" :class="{ 'is-flipped': isFlipped }" @click="toggleFlip">
    <div class="card-inner">
      <div class="card-front">
        <h3 class="card-title">{{ title }}</h3>
        <span class="click-indicator">点击揭晓</span>
      </div>
      <div class="card-back">
        <div class="card-content">
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, withDefaults } from 'vue'

const props = withDefaults(
  defineProps<{
    title: string
    initialFlipped?: boolean
  }>(),
  {
    initialFlipped: false,
  },
)

const isFlipped = ref(props.initialFlipped)

function toggleFlip() {
  isFlipped.value = !isFlipped.value
}
</script>

<style scoped>
.card {
  background-color: transparent;
  min-height: 150px;
  perspective: 1000px;
  cursor: pointer;
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.card:hover {
  transform: translateY(-5px);
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: left;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.card.is-flipped .card-inner {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
}

.card-front {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: #333;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.card-title {
  font-size: 1.3em;
  margin: 0;
  color: #005a9e;
  font-weight: bold;
}

.click-indicator {
  font-size: 0.9em;
  color: #007acc;
  margin-top: 10px;
  font-weight: 500;
}

.card-back {
  background-color: #ffffff;
  color: #333;
  transform: rotateY(180deg);
  padding: 20px;
  overflow-y: auto;
}

.card-content {
  height: 100%;
  overflow-y: auto;
}
</style>
