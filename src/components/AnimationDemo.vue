<script setup lang="ts">
interface Step {
  id: number
  title: string
  description: string
}

interface Props {
  currentStep: number
  steps: Step[]
  isPlaying: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'next'): void
  (e: 'prev'): void
  (e: 'play'): void
  (e: 'reset'): void
}>()

const handleNext = () => emit('next')
const handlePrev = () => emit('prev')
const handlePlay = () => emit('play')
const handleReset = () => emit('reset')
</script>

<template>
  <div class="animation-demo">
    <div class="demo-container">
      <!-- SVG动画区域 -->
      <div class="animation-area">
        <svg width="100%" height="400" viewBox="0 0 800 400" class="demo-svg">
          <!-- 背景 -->
          <rect width="800" height="400" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="10" />

          <!-- 步骤0：传统字符串拼接 -->
          <g v-show="currentStep === 0" class="step-group">
            <text x="400" y="40" text-anchor="middle" class="title-text">
              传统字符串拼接 - 痛苦的过程
            </text>

            <!-- 代码片段 -->
            <g class="code-fragment">
              <rect
                x="50"
                y="80"
                width="140"
                height="40"
                fill="#ff6b6b"
                stroke="#ff5252"
                stroke-width="2"
                rx="5"
              />
              <text x="120" y="105" text-anchor="middle" class="code-text">String s = "行1\n"</text>
            </g>

            <g class="code-fragment">
              <rect
                x="50"
                y="140"
                width="140"
                height="40"
                fill="#ff6b6b"
                stroke="#ff5252"
                stroke-width="2"
                rx="5"
              />
              <text x="120" y="165" text-anchor="middle" class="code-text">+ "行2\n"</text>
            </g>

            <g class="code-fragment">
              <rect
                x="50"
                y="200"
                width="140"
                height="40"
                fill="#ff6b6b"
                stroke="#ff5252"
                stroke-width="2"
                rx="5"
              />
              <text x="120" y="225" text-anchor="middle" class="code-text">+ "行3";</text>
            </g>

            <!-- 箭头 -->
            <path
              d="M 210 160 L 280 160"
              stroke="#666"
              stroke-width="3"
              fill="none"
              marker-end="url(#arrowhead)"
            />

            <!-- 结果 -->
            <rect
              x="300"
              y="120"
              width="200"
              height="80"
              fill="#ffd93d"
              stroke="#ffc107"
              stroke-width="2"
              rx="5"
            />
            <text x="400" y="145" text-anchor="middle" class="result-text">难以阅读和维护</text>
            <text x="400" y="170" text-anchor="middle" class="result-text">容易出错</text>
            <text x="400" y="195" text-anchor="middle" class="result-text">需要转义字符</text>

            <!-- 痛苦表情 -->
            <circle cx="600" cy="160" r="40" fill="#ffcdd2" />
            <text x="600" y="175" text-anchor="middle" class="emoji-text">😫</text>
          </g>

          <!-- 步骤1：文本块的优雅解决 -->
          <g v-show="currentStep === 1" class="step-group">
            <text x="400" y="40" text-anchor="middle" class="title-text">
              文本块 - 优雅的解决方案
            </text>

            <!-- 文本块 -->
            <rect
              x="100"
              y="80"
              width="250"
              height="120"
              fill="#4caf50"
              stroke="#388e3c"
              stroke-width="2"
              rx="5"
            />
            <text x="110" y="105" class="text-block-content">String s = """</text>
            <text x="130" y="125" class="text-block-content">行1</text>
            <text x="130" y="145" class="text-block-content">行2</text>
            <text x="130" y="165" class="text-block-content">行3</text>
            <text x="130" y="185" class="text-block-content">""";</text>

            <!-- 箭头 -->
            <path
              d="M 370 140 L 440 140"
              stroke="#666"
              stroke-width="3"
              fill="none"
              marker-end="url(#arrowhead)"
            />

            <!-- 优势 -->
            <rect
              x="460"
              y="100"
              width="200"
              height="80"
              fill="#c8e6c9"
              stroke="#4caf50"
              stroke-width="2"
              rx="5"
            />
            <text x="560" y="125" text-anchor="middle" class="benefit-text">✓ 易于阅读</text>
            <text x="560" y="145" text-anchor="middle" class="benefit-text">✓ 无需转义</text>
            <text x="560" y="165" text-anchor="middle" class="benefit-text">✓ 自然缩进</text>

            <!-- 开心表情 -->
            <circle cx="600" cy="240" r="30" fill="#c8e6c9" />
            <text x="600" y="255" text-anchor="middle" class="emoji-text">😊</text>
          </g>

          <!-- 步骤2：编译器处理过程 -->
          <g v-show="currentStep === 2" class="step-group">
            <text x="400" y="40" text-anchor="middle" class="title-text">编译器的三步魔法处理</text>

            <!-- 步骤1 -->
            <rect
              x="50"
              y="80"
              width="200"
              height="60"
              fill="#e3f2fd"
              stroke="#2196f3"
              stroke-width="2"
              rx="5"
            />
            <text x="150" y="105" text-anchor="middle" class="process-title">1. 行终止符转换</text>
            <text x="150" y="125" text-anchor="middle" class="process-desc">统一为LF (\n)</text>

            <!-- 步骤2 -->
            <rect
              x="300"
              y="80"
              width="200"
              height="60"
              fill="#f3e5f5"
              stroke="#9c27b0"
              stroke-width="2"
              rx="5"
            />
            <text x="400" y="105" text-anchor="middle" class="process-title">2. 移除附带空白</text>
            <text x="400" y="125" text-anchor="middle" class="process-desc">保持相对缩进</text>

            <!-- 步骤3 -->
            <rect
              x="550"
              y="80"
              width="200"
              height="60"
              fill="#fff3e0"
              stroke="#ff9800"
              stroke-width="2"
              rx="5"
            />
            <text x="650" y="105" text-anchor="middle" class="process-title">3. 转义序列处理</text>
            <text x="650" y="125" text-anchor="middle" class="process-desc">最后处理\t, \s等</text>

            <!-- 流程箭头 -->
            <path
              d="M 250 110 L 290 110"
              stroke="#666"
              stroke-width="2"
              fill="none"
              marker-end="url(#arrowhead)"
            />
            <path
              d="M 500 110 L 540 110"
              stroke="#666"
              stroke-width="2"
              fill="none"
              marker-end="url(#arrowhead)"
            />

            <!-- 最终结果 -->
            <rect
              x="200"
              y="200"
              width="400"
              height="80"
              fill="#e8f5e8"
              stroke="#4caf50"
              stroke-width="2"
              rx="5"
            />
            <text x="400" y="230" text-anchor="middle" class="final-result">
              最终生成标准的java.lang.String对象
            </text>
            <text x="400" y="255" text-anchor="middle" class="final-result">
              与传统字符串完全相同！
            </text>
          </g>

          <!-- 步骤3：最终结果 -->
          <g v-show="currentStep === 3" class="step-group">
            <text x="400" y="40" text-anchor="middle" class="title-text">
              运行时完全相同 - 这就是编译时魔法！
            </text>

            <!-- 左侧：传统方式 -->
            <rect
              x="50"
              y="100"
              width="300"
              height="120"
              fill="#ffebee"
              stroke="#f44336"
              stroke-width="2"
              rx="5"
            />
            <text x="200" y="125" text-anchor="middle" class="comparison-title">
              传统字符串拼接
            </text>
            <text x="200" y="150" text-anchor="middle" class="comparison-content">复杂的代码</text>
            <text x="200" y="175" text-anchor="middle" class="comparison-content">多行拼接</text>
            <text x="200" y="200" text-anchor="middle" class="comparison-content">转义字符</text>

            <!-- 右侧：文本块 -->
            <rect
              x="450"
              y="100"
              width="300"
              height="120"
              fill="#e8f5e8"
              stroke="#4caf50"
              stroke-width="2"
              rx="5"
            />
            <text x="600" y="125" text-anchor="middle" class="comparison-title">文本块方式</text>
            <text x="600" y="150" text-anchor="middle" class="comparison-content">简洁优雅</text>
            <text x="600" y="175" text-anchor="middle" class="comparison-content">自然书写</text>
            <text x="600" y="200" text-anchor="middle" class="comparison-content">无需转义</text>

            <!-- 等号 -->
            <circle cx="400" cy="160" r="30" fill="#fff3e0" stroke="#ff9800" stroke-width="3" />
            <text x="400" y="170" text-anchor="middle" class="equals-sign">=</text>

            <!-- 下方强调 -->
            <rect
              x="150"
              y="280"
              width="500"
              height="60"
              fill="#f0f4ff"
              stroke="#3f51b5"
              stroke-width="2"
              rx="5"
            />
            <text x="400" y="305" text-anchor="middle" class="emphasis-text">
              🎉 编译后生成的字节码完全相同！
            </text>
            <text x="400" y="325" text-anchor="middle" class="emphasis-text">
              性能零差异，只是开发体验大提升！
            </text>
          </g>

          <!-- 箭头标记定义 -->
          <defs>
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="10"
              refY="3.5"
              orient="auto"
            >
              <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
            </marker>
          </defs>
        </svg>
      </div>

      <!-- 步骤说明 -->
      <div class="step-info">
        <div class="step-header">
          <h3 class="step-title">{{ steps[currentStep]?.title }}</h3>
          <div class="step-counter">{{ currentStep + 1 }} / {{ steps.length }}</div>
        </div>
        <p class="step-description">{{ steps[currentStep]?.description }}</p>
      </div>

      <!-- 控制按钮 -->
      <div class="controls">
        <button @click="handlePrev" :disabled="currentStep === 0" class="control-btn prev-btn">
          ⬅️ 上一步
        </button>

        <button @click="handlePlay" :disabled="isPlaying" class="control-btn play-btn">
          {{ isPlaying ? '播放中...' : '🎬 自动播放' }}
        </button>

        <button
          @click="handleNext"
          :disabled="currentStep === steps.length - 1"
          class="control-btn next-btn"
        >
          下一步 ➡️
        </button>

        <button @click="handleReset" class="control-btn reset-btn">🔄 重置</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.animation-demo {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 900px;
  margin: 0 auto;
}

.demo-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.animation-area {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 1rem;
  overflow: hidden;
}

.demo-svg {
  max-width: 100%;
  height: auto;
}

/* SVG文本样式 */
.title-text {
  font-size: 18px;
  font-weight: 600;
  fill: #333;
}

.code-text {
  font-size: 12px;
  font-family: 'Courier New', monospace;
  fill: white;
  font-weight: 500;
}

.text-block-content {
  font-size: 12px;
  font-family: 'Courier New', monospace;
  fill: white;
  font-weight: 500;
}

.result-text {
  font-size: 11px;
  fill: #333;
}

.benefit-text {
  font-size: 12px;
  fill: #2e7d32;
  font-weight: 500;
}

.emoji-text {
  font-size: 24px;
}

.process-title {
  font-size: 12px;
  font-weight: 600;
  fill: #333;
}

.process-desc {
  font-size: 10px;
  fill: #666;
}

.final-result {
  font-size: 14px;
  font-weight: 500;
  fill: #2e7d32;
}

.comparison-title {
  font-size: 14px;
  font-weight: 600;
  fill: #333;
}

.comparison-content {
  font-size: 11px;
  fill: #666;
}

.equals-sign {
  font-size: 20px;
  font-weight: bold;
  fill: #ff9800;
}

.emphasis-text {
  font-size: 14px;
  font-weight: 600;
  fill: #3f51b5;
}

/* 步骤信息 */
.step-info {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.step-title {
  color: #333;
  font-size: 1.3rem;
  margin: 0;
  font-weight: 600;
}

.step-counter {
  background: #667eea;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 500;
}

.step-description {
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

/* 控制按钮 */
.controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.control-btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.prev-btn {
  background: #e3f2fd;
  color: #1976d2;
}

.prev-btn:hover:not(:disabled) {
  background: #bbdefb;
  transform: translateY(-2px);
}

.next-btn {
  background: #e8f5e8;
  color: #388e3c;
}

.next-btn:hover:not(:disabled) {
  background: #c8e6c9;
  transform: translateY(-2px);
}

.play-btn {
  background: #fff3e0;
  color: #f57c00;
}

.play-btn:hover:not(:disabled) {
  background: #ffe0b2;
  transform: translateY(-2px);
}

.reset-btn {
  background: #fce4ec;
  color: #c2185b;
}

.reset-btn:hover {
  background: #f8bbd9;
  transform: translateY(-2px);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 动画效果 */
.step-group {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .animation-demo {
    padding: 1rem;
  }

  .controls {
    gap: 0.5rem;
  }

  .control-btn {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
    min-width: 100px;
  }

  .step-title {
    font-size: 1.1rem;
  }

  .demo-svg {
    height: 300px;
  }
}
</style>
