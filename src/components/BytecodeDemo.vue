<template>
  <div class="bytecode-demo">
    <div class="demo-container">
      <h4>🔧 字节码与类文件互动演示</h4>

      <!-- 演示选择器 -->
      <div class="demo-selector">
        <button
          v-for="(demo, index) in demos"
          :key="index"
          @click="selectDemo(index)"
          :class="['demo-btn', { active: selectedDemo === index }]"
        >
          {{ demo.name }}
        </button>
      </div>

      <!-- 演示区域 -->
      <div class="demo-area">
        <!-- 类加载过程演示 -->
        <div v-if="selectedDemo === 0" class="class-loading-demo">
          <h5>🚀 类加载过程可视化</h5>
          <div class="loading-stages">
            <div
              v-for="(stage, index) in loadingStages"
              :key="index"
              :class="[
                'stage-card',
                { active: currentStage >= index, completed: currentStage > index },
              ]"
              @click="setCurrentStage(index)"
            >
              <div class="stage-number">{{ index + 1 }}</div>
              <div class="stage-content">
                <h6>{{ stage.name }}</h6>
                <p>{{ stage.description }}</p>
                <div v-if="currentStage === index" class="stage-details">
                  <ul>
                    <li v-for="detail in stage.details" :key="detail">{{ detail }}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="loading-controls">
            <button @click="startAnimation" class="control-btn">▶️ 开始演示</button>
            <button @click="resetAnimation" class="control-btn">🔄 重置</button>
          </div>
        </div>

        <!-- 类加载器层级演示 -->
        <div v-if="selectedDemo === 1" class="classloader-hierarchy">
          <h5>🏗️ 类加载器层级与双亲委派</h5>
          <div class="hierarchy-visualization">
            <div class="classloader-tree">
              <div
                v-for="(loader, index) in classLoaders"
                :key="index"
                :class="['loader-node', { active: selectedLoader === index }]"
                @click="selectLoader(index)"
              >
                <div class="loader-icon">{{ loader.icon }}</div>
                <div class="loader-info">
                  <h6>{{ loader.name }}</h6>
                  <p>{{ loader.description }}</p>
                </div>
              </div>
            </div>
            <div class="delegation-flow">
              <h6>双亲委派流程</h6>
              <div class="flow-steps">
                <div
                  v-for="(step, index) in delegationSteps"
                  :key="index"
                  :class="['flow-step', { active: currentDelegationStep === index }]"
                >
                  <div class="step-number">{{ index + 1 }}</div>
                  <div class="step-text">{{ step }}</div>
                </div>
              </div>
              <button @click="startDelegation" class="delegation-btn">🔄 演示委派过程</button>
            </div>
          </div>
        </div>

        <!-- javap工具演示 -->
        <div v-if="selectedDemo === 2" class="javap-demo">
          <h5>🔍 javap 工具解析演示</h5>
          <div class="javap-interface">
            <div class="source-code">
              <h6>Java源代码</h6>
              <pre class="code-block">{{ sampleJavaCode }}</pre>
            </div>
            <div class="javap-options">
              <h6>javap 选项</h6>
              <div class="option-buttons">
                <button
                  v-for="option in javapOptions"
                  :key="option.flag"
                  @click="selectJavapOption(option.flag)"
                  :class="['option-btn', { active: selectedJavapOption === option.flag }]"
                >
                  {{ option.flag }} - {{ option.description }}
                </button>
              </div>
            </div>
            <div class="javap-output">
              <h6>javap {{ selectedJavapOption }} 输出</h6>
              <pre class="output-block">{{ getJavapOutput() }}</pre>
            </div>
          </div>
        </div>

        <!-- 字节码指令演示 -->
        <div v-if="selectedDemo === 3" class="bytecode-instructions">
          <h5>⚙️ 字节码指令执行模拟</h5>
          <div class="bytecode-simulator">
            <div class="code-input">
              <h6>选择示例代码</h6>
              <select v-model="selectedBytecodeExample" class="example-selector">
                <option v-for="(example, index) in bytecodeExamples" :key="index" :value="index">
                  {{ example.name }}
                </option>
              </select>
              <div class="java-code">
                <h6>Java代码</h6>
                <pre class="code-block">{{
                  bytecodeExamples[selectedBytecodeExample].javaCode
                }}</pre>
              </div>
            </div>
            <div class="execution-area">
              <div class="operand-stack">
                <h6>操作数栈</h6>
                <div class="stack-visualization">
                  <div v-for="(item, index) in operandStack" :key="index" class="stack-item">
                    {{ item }}
                  </div>
                  <div v-if="operandStack.length === 0" class="stack-empty">栈为空</div>
                </div>
              </div>
              <div class="local-variables">
                <h6>局部变量表</h6>
                <div class="variables-table">
                  <div
                    v-for="(variable, index) in localVariables"
                    :key="index"
                    class="variable-slot"
                  >
                    <span class="slot-index">{{ index }}</span>
                    <span class="slot-value">{{ variable || 'null' }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="bytecode-instructions-panel">
              <h6>字节码指令</h6>
              <div class="instructions-list">
                <div
                  v-for="(instruction, index) in bytecodeExamples[selectedBytecodeExample]
                    .instructions"
                  :key="index"
                  :class="[
                    'instruction-item',
                    { current: currentInstruction === index, executed: currentInstruction > index },
                  ]"
                >
                  <span class="instruction-index">{{ index }}</span>
                  <span class="instruction-opcode">{{ instruction.opcode }}</span>
                  <span class="instruction-description">{{ instruction.description }}</span>
                </div>
              </div>
              <div class="execution-controls">
                <button @click="stepExecution" class="step-btn">▶️ 单步执行</button>
                <button @click="runExecution" class="run-btn">🚀 运行</button>
                <button @click="resetExecution" class="reset-btn">🔄 重置</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 反射机制演示 -->
        <div v-if="selectedDemo === 4" class="reflection-demo">
          <h5>🪞 反射机制演示</h5>
          <div class="reflection-interface">
            <div class="target-class">
              <h6>目标类</h6>
              <select v-model="selectedReflectionClass" class="class-selector">
                <option v-for="(cls, index) in reflectionClasses" :key="index" :value="index">
                  {{ cls.name }}
                </option>
              </select>
              <div class="class-info">
                <pre class="code-block">{{ reflectionClasses[selectedReflectionClass].code }}</pre>
              </div>
            </div>
            <div class="reflection-operations">
              <h6>反射操作</h6>
              <div class="operation-tabs">
                <button
                  v-for="(op, index) in reflectionOperations"
                  :key="index"
                  @click="selectReflectionOperation(index)"
                  :class="['op-tab', { active: selectedReflectionOp === index }]"
                >
                  {{ op.name }}
                </button>
              </div>
              <div class="operation-content">
                <div class="operation-code">
                  <h6>反射代码</h6>
                  <pre class="code-block">{{
                    reflectionOperations[selectedReflectionOp].code
                  }}</pre>
                </div>
                <div class="operation-result">
                  <h6>执行结果</h6>
                  <button @click="executeReflection" class="execute-btn">🔍 执行反射</button>
                  <div v-if="reflectionResult" class="result-display">
                    <pre class="result-block">{{ reflectionResult }}</pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// 响应式数据
const selectedDemo = ref(0)
const currentStage = ref(-1)
const selectedLoader = ref(0)
const currentDelegationStep = ref(-1)
const selectedJavapOption = ref('-c')
const selectedBytecodeExample = ref(0)
const currentInstruction = ref(-1)
const operandStack = ref<(string | number)[]>([])
const localVariables = ref<(string | number | null)[]>([null, null, null, null])
const selectedReflectionClass = ref(0)
const selectedReflectionOp = ref(0)
const reflectionResult = ref('')

// 演示列表
const demos = [
  { name: '类加载过程', icon: '🚀' },
  { name: '类加载器层级', icon: '🏗️' },
  { name: 'javap工具', icon: '🔍' },
  { name: '字节码执行', icon: '⚙️' },
  { name: '反射机制', icon: '🪞' },
]

// 类加载阶段
const loadingStages = [
  {
    name: '加载 (Loading)',
    description: '读取.class文件，创建Class对象',
    details: [
      '通过类的全限定名获取二进制字节流',
      '将字节流转换为方法区的运行时数据结构',
      '在堆中生成java.lang.Class对象',
    ],
  },
  {
    name: '验证 (Verification)',
    description: '确保字节码安全合规',
    details: [
      '文件格式验证：魔数、版本号等',
      '元数据验证：语义分析',
      '字节码验证：数据流和控制流分析',
      '符号引用验证：匹配性校验',
    ],
  },
  {
    name: '准备 (Preparation)',
    description: '为静态变量分配内存并设置零值',
    details: [
      '为类的静态变量分配内存',
      '设置变量的初始零值',
      '不执行任何Java代码',
      '不包括实例变量',
    ],
  },
  {
    name: '解析 (Resolution)',
    description: '将符号引用转换为直接引用',
    details: ['类或接口的解析', '字段解析', '方法解析', '接口方法解析'],
  },
  {
    name: '初始化 (Initialization)',
    description: '执行类构造器<clinit>()方法',
    details: ['执行静态变量赋值语句', '执行静态代码块', '保证父类先初始化', '线程安全的执行'],
  },
]

// 类加载器
const classLoaders = [
  {
    name: 'Bootstrap ClassLoader',
    icon: '🏛️',
    description: '启动类加载器，加载核心Java类库',
  },
  {
    name: 'Platform ClassLoader',
    icon: '🏢',
    description: '平台类加载器，加载扩展库',
  },
  {
    name: 'Application ClassLoader',
    icon: '📱',
    description: '应用类加载器，加载用户类路径上的类',
  },
  {
    name: 'Custom ClassLoader',
    icon: '🔧',
    description: '自定义类加载器，实现特殊加载逻辑',
  },
]

// 双亲委派步骤
const delegationSteps = [
  '自定义类加载器收到加载请求',
  '委派给父加载器（Application）',
  '继续委派给父加载器（Platform）',
  '最终委派给Bootstrap加载器',
  'Bootstrap尝试加载，失败则返回',
  'Platform尝试加载，失败则返回',
  'Application尝试加载，失败则返回',
  '自定义加载器自己尝试加载',
]

// javap选项
const javapOptions = [
  { flag: '-c', description: '显示字节码指令' },
  { flag: '-v', description: '显示详细信息' },
  { flag: '-p', description: '显示所有成员' },
  { flag: '-s', description: '显示内部类型签名' },
]

// 示例Java代码
const sampleJavaCode = `public class Calculator {
    private int value = 0;

    public int add(int a, int b) {
        return a + b;
    }

    public static void main(String[] args) {
        Calculator calc = new Calculator();
        int result = calc.add(1, 2);
        System.out.println(result);
    }
}`

// 字节码示例
const bytecodeExamples = [
  {
    name: '简单加法 (1 + 2)',
    javaCode: 'int result = 1 + 2;',
    instructions: [
      { opcode: 'iconst_1', description: '将常量1压入栈顶' },
      { opcode: 'iconst_2', description: '将常量2压入栈顶' },
      { opcode: 'iadd', description: '弹出栈顶两个int值相加，结果压入栈顶' },
      { opcode: 'istore_1', description: '将栈顶int值存入局部变量表索引1' },
    ],
  },
  {
    name: '方法调用',
    javaCode: 'String s = "Hello".toUpperCase();',
    instructions: [
      { opcode: 'ldc', description: '从常量池加载"Hello"字符串' },
      { opcode: 'invokevirtual', description: '调用String.toUpperCase()方法' },
      { opcode: 'astore_1', description: '将结果存入局部变量表索引1' },
    ],
  },
  {
    name: '条件分支',
    javaCode: 'if (x > 0) return 1; else return -1;',
    instructions: [
      { opcode: 'iload_0', description: '加载局部变量x到栈顶' },
      { opcode: 'ifle', description: '如果栈顶值<=0则跳转' },
      { opcode: 'iconst_1', description: '将常量1压入栈顶' },
      { opcode: 'ireturn', description: '返回栈顶int值' },
      { opcode: 'iconst_m1', description: '将常量-1压入栈顶' },
      { opcode: 'ireturn', description: '返回栈顶int值' },
    ],
  },
]

// 反射类示例
const reflectionClasses = [
  {
    name: 'Person',
    code: `public class Person {
    private String name;
    private int age;

    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public int getAge() { return age; }
    public void setAge(int age) { this.age = age; }

    private void secretMethod() {
        System.out.println("This is a secret!");
    }
}`,
  },
  {
    name: 'Calculator',
    code: `public class Calculator {
    public static final double PI = 3.14159;
    private double result = 0;

    public double add(double a, double b) {
        result = a + b;
        return result;
    }

    public double getResult() { return result; }
}`,
  },
]

// 反射操作
const reflectionOperations = [
  {
    name: '获取类信息',
    code: `Class<?> clazz = Person.class;
String className = clazz.getName();
String simpleName = clazz.getSimpleName();
Package pkg = clazz.getPackage();`,
  },
  {
    name: '获取字段信息',
    code: `Class<?> clazz = Person.class;
Field[] fields = clazz.getDeclaredFields();
for (Field field : fields) {
    System.out.println(field.getName() + " : " + field.getType());
}`,
  },
  {
    name: '获取方法信息',
    code: `Class<?> clazz = Person.class;
Method[] methods = clazz.getDeclaredMethods();
for (Method method : methods) {
    System.out.println(method.getName() + " : " + method.getReturnType());
}`,
  },
  {
    name: '创建实例并调用',
    code: `Class<?> clazz = Person.class;
Constructor<?> constructor = clazz.getConstructor(String.class, int.class);
Object person = constructor.newInstance("Alice", 25);

Method getName = clazz.getMethod("getName");
String name = (String) getName.invoke(person);`,
  },
  {
    name: '访问私有成员',
    code: `Class<?> clazz = Person.class;
Object person = clazz.getConstructor(String.class, int.class)
    .newInstance("Bob", 30);

Method secretMethod = clazz.getDeclaredMethod("secretMethod");
secretMethod.setAccessible(true);
secretMethod.invoke(person);`,
  },
]

// 方法
const selectDemo = (index: number) => {
  selectedDemo.value = index
  resetAllStates()
}

const resetAllStates = () => {
  currentStage.value = -1
  currentDelegationStep.value = -1
  currentInstruction.value = -1
  operandStack.value = []
  localVariables.value = [null, null, null, null]
  reflectionResult.value = ''
}

const setCurrentStage = (index: number) => {
  currentStage.value = index
}

const startAnimation = () => {
  currentStage.value = 0
  const interval = setInterval(() => {
    if (currentStage.value < loadingStages.length - 1) {
      currentStage.value++
    } else {
      clearInterval(interval)
    }
  }, 1500)
}

const resetAnimation = () => {
  currentStage.value = -1
}

const selectLoader = (index: number) => {
  selectedLoader.value = index
}

const startDelegation = () => {
  currentDelegationStep.value = 0
  const interval = setInterval(() => {
    if (currentDelegationStep.value < delegationSteps.length - 1) {
      currentDelegationStep.value++
    } else {
      clearInterval(interval)
    }
  }, 800)
}

const selectJavapOption = (flag: string) => {
  selectedJavapOption.value = flag
}

const getJavapOutput = () => {
  const outputs = {
    '-c': `Compiled from "Calculator.java"
public class Calculator {
  public Calculator();
    Code:
       0: aload_0
       1: invokespecial #1  // Method java/lang/Object."<init>":()V
       4: aload_0
       5: iconst_0
       6: putfield      #2  // Field value:I
       9: return

  public int add(int, int);
    Code:
       0: iload_1
       1: iload_2
       2: iadd
       3: ireturn
}`,
    '-v': `Classfile Calculator.class
  Last modified 2024-01-01; size 425 bytes
  MD5 checksum 1234567890abcdef
  Compiled from "Calculator.java"
public class Calculator
  minor version: 0
  major version: 61
  flags: (0x0021) ACC_PUBLIC, ACC_SUPER
  this_class: #2                          // Calculator
  super_class: #3                         // java/lang/Object
  interfaces: 0, fields: 1, methods: 3, attributes: 1
Constant pool:
   #1 = Methodref          #3.#15         // java/lang/Object."<init>":()V
   #2 = Class              #16            // Calculator
   #3 = Class              #17            // java/lang/Object`,
    '-p': `Compiled from "Calculator.java"
public class Calculator {
  private int value;
  public Calculator();
  public int add(int, int);
  public static void main(java.lang.String[]);
}`,
    '-s': `Compiled from "Calculator.java"
public class Calculator {
  private int value;
    descriptor: I
  public Calculator();
    descriptor: ()V
  public int add(int, int);
    descriptor: (II)I
  public static void main(java.lang.String[]);
    descriptor: ([Ljava/lang/String;)V
}`,
  }
  return outputs[selectedJavapOption.value as keyof typeof outputs] || ''
}

const stepExecution = () => {
  const example = bytecodeExamples[selectedBytecodeExample.value]
  if (currentInstruction.value < example.instructions.length - 1) {
    currentInstruction.value++
    executeInstruction(example.instructions[currentInstruction.value])
  }
}

const runExecution = () => {
  const example = bytecodeExamples[selectedBytecodeExample.value]
  const interval = setInterval(() => {
    if (currentInstruction.value < example.instructions.length - 1) {
      currentInstruction.value++
      executeInstruction(example.instructions[currentInstruction.value])
    } else {
      clearInterval(interval)
    }
  }, 1000)
}

const resetExecution = () => {
  currentInstruction.value = -1
  operandStack.value = []
  localVariables.value = [null, null, null, null]
}

const executeInstruction = (instruction: any) => {
  switch (instruction.opcode) {
    case 'iconst_1':
      operandStack.value.push(1)
      break
    case 'iconst_2':
      operandStack.value.push(2)
      break
    case 'iconst_m1':
      operandStack.value.push(-1)
      break
    case 'iadd':
      if (operandStack.value.length >= 2) {
        const b = operandStack.value.pop()!
        const a = operandStack.value.pop()!
        operandStack.value.push((a as number) + (b as number))
      }
      break
    case 'istore_1':
      if (operandStack.value.length > 0) {
        localVariables.value[1] = operandStack.value.pop()!
      }
      break
    case 'iload_0':
      if (localVariables.value[0] !== null) {
        operandStack.value.push(localVariables.value[0])
      } else {
        operandStack.value.push(5) // 假设x=5
        localVariables.value[0] = 5
      }
      break
    case 'ldc':
      operandStack.value.push('"Hello"')
      break
    case 'invokevirtual':
      if (operandStack.value.length > 0) {
        operandStack.value.pop()
        operandStack.value.push('"HELLO"')
      }
      break
    case 'astore_1':
      if (operandStack.value.length > 0) {
        localVariables.value[1] = operandStack.value.pop()!
      }
      break
    case 'ifle':
      if (operandStack.value.length > 0) {
        const value = operandStack.value.pop()!
        if ((value as number) <= 0) {
          // 跳转逻辑（简化）
          currentInstruction.value = 4
        }
      }
      break
    case 'ireturn':
      // 返回逻辑（简化显示）
      break
  }
}

const selectReflectionOperation = (index: number) => {
  selectedReflectionOp.value = index
  reflectionResult.value = ''
}

const executeReflection = () => {
  const results = [
    `类名: Person
简单名: Person
包名: com.example`,
    `字段信息:
name : class java.lang.String
age : int`,
    `方法信息:
getName : class java.lang.String
setName : void
getAge : int
setAge : void
secretMethod : void`,
    `创建实例: Person@1a2b3c4d
调用getName(): Alice`,
    `访问私有方法成功!
输出: This is a secret!`,
  ]

  reflectionResult.value = results[selectedReflectionOp.value] || '执行完成'
}
</script>

<style scoped>
.bytecode-demo {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.demo-container h4 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
}

.demo-selector {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.demo-btn {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #495057;
}

.demo-btn:hover {
  background: #f8f9fa;
  border-color: #667eea;
  transform: translateY(-2px);
}

.demo-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.demo-area {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.demo-area h5 {
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* 类加载过程样式 */
.loading-stages {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.stage-card {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 12px;
  border: 2px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.stage-card:hover {
  border-color: #667eea;
  transform: translateX(5px);
}

.stage-card.active {
  border-color: #667eea;
  background: #e3f2fd;
}

.stage-card.completed {
  border-color: #28a745;
  background: #d4edda;
}

.stage-number {
  width: 40px;
  height: 40px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.stage-card.completed .stage-number {
  background: #28a745;
}

.stage-content h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.stage-content p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.stage-details {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.stage-details ul {
  margin: 0;
  padding-left: 1.5rem;
}

.stage-details li {
  margin-bottom: 0.5rem;
  color: #555;
}

.loading-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.control-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.control-btn:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
}

/* 类加载器层级样式 */
.hierarchy-visualization {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.classloader-tree {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.loader-node {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 12px;
  border: 2px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.loader-node:hover {
  border-color: #667eea;
}

.loader-node.active {
  border-color: #667eea;
  background: #e3f2fd;
}

.loader-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.loader-info h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.loader-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.delegation-flow {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.delegation-flow h6 {
  margin: 0 0 1rem 0;
  color: #333;
  text-align: center;
}

.flow-steps {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.flow-step {
  display: flex;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.flow-step.active {
  background: #667eea;
  color: white;
}

.step-number {
  width: 24px;
  height: 24px;
  background: #e9ecef;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  flex-shrink: 0;
}

.flow-step.active .step-number {
  background: white;
  color: #667eea;
}

.step-text {
  font-size: 0.9rem;
  line-height: 1.4;
}

.delegation-btn {
  width: 100%;
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.delegation-btn:hover {
  background: #5a6fd8;
}

/* javap工具样式 */
.javap-interface {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.javap-interface > div {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.javap-interface h6 {
  margin: 0 0 1rem 0;
  color: #333;
}

.code-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-width: 100%;
  box-sizing: border-box;
}

.option-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.option-btn {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 0.9rem;
}

.option-btn:hover {
  border-color: #667eea;
}

.option-btn.active {
  border-color: #667eea;
  background: #e3f2fd;
}

.javap-output {
  grid-column: 1 / -1;
}

.output-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-width: 100%;
  box-sizing: border-box;
  max-height: 400px;
  overflow-y: auto;
}

/* 字节码执行样式 */
.bytecode-simulator {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1.5rem;
}

.code-input,
.execution-area,
.bytecode-instructions-panel {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.example-selector {
  width: 100%;
  padding: 0.5rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.java-code h6 {
  margin: 1rem 0 0.5rem 0;
  color: #333;
}

.operand-stack h6,
.local-variables h6,
.bytecode-instructions-panel h6 {
  margin: 0 0 1rem 0;
  color: #333;
  text-align: center;
}

.stack-visualization {
  min-height: 150px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column-reverse;
  gap: 0.5rem;
}

.stack-item {
  background: #667eea;
  color: white;
  padding: 0.5rem;
  border-radius: 6px;
  text-align: center;
  font-weight: 500;
  animation: stackPush 0.3s ease;
}

@keyframes stackPush {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stack-empty {
  color: #999;
  text-align: center;
  font-style: italic;
  margin-top: auto;
  margin-bottom: auto;
}

.variables-table {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.variable-slot {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: white;
}

.slot-index {
  width: 20px;
  height: 20px;
  background: #6c757d;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
}

.slot-value {
  flex: 1;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.instructions-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.instruction-item {
  display: grid;
  grid-template-columns: 30px 80px 1fr;
  gap: 0.5rem;
  padding: 0.5rem;
  border-bottom: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.instruction-item:last-child {
  border-bottom: none;
}

.instruction-item.current {
  background: #fff3cd;
  border-color: #ffc107;
}

.instruction-item.executed {
  background: #d4edda;
  color: #155724;
}

.instruction-index {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.8rem;
}

.instruction-opcode {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #667eea;
  font-size: 0.8rem;
}

.instruction-description {
  font-size: 0.8rem;
  color: #555;
}

.execution-controls {
  display: flex;
  gap: 0.5rem;
}

.step-btn,
.run-btn,
.reset-btn {
  flex: 1;
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  font-weight: 500;
}

.step-btn {
  background: #28a745;
  color: white;
}

.step-btn:hover {
  background: #218838;
}

.run-btn {
  background: #007bff;
  color: white;
}

.run-btn:hover {
  background: #0056b3;
}

.reset-btn {
  background: #6c757d;
  color: white;
}

.reset-btn:hover {
  background: #545b62;
}

/* 反射演示样式 */
.reflection-interface {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.target-class,
.reflection-operations {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.target-class h6,
.reflection-operations h6 {
  margin: 0 0 1rem 0;
  color: #333;
}

.class-selector {
  width: 100%;
  padding: 0.5rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.class-info {
  margin-top: 1rem;
}

.operation-tabs {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.op-tab {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-weight: 500;
}

.op-tab:hover {
  border-color: #667eea;
}

.op-tab.active {
  border-color: #667eea;
  background: #e3f2fd;
}

.operation-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.operation-code h6,
.operation-result h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.execute-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  margin-bottom: 1rem;
}

.execute-btn:hover {
  background: #5a6fd8;
}

.result-display {
  margin-top: 1rem;
}

.result-block {
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  border: 1px solid #c3e6cb;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-width: 100%;
  box-sizing: border-box;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-selector {
    flex-direction: column;
    align-items: center;
  }

  .hierarchy-visualization {
    grid-template-columns: 1fr;
  }

  .javap-interface {
    grid-template-columns: 1fr;
  }

  .bytecode-simulator {
    grid-template-columns: 1fr;
  }

  .reflection-interface {
    grid-template-columns: 1fr;
  }

  .variables-table {
    grid-template-columns: 1fr;
  }

  .execution-controls {
    flex-direction: column;
  }

  .code-block,
  .output-block {
    font-size: 0.7rem;
    padding: 0.75rem;
  }

  .stage-card {
    flex-direction: column;
    text-align: center;
  }

  .stage-number {
    align-self: center;
  }

  .loader-node {
    flex-direction: column;
    text-align: center;
  }

  .loader-icon {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .bytecode-demo {
    padding: 1rem;
  }

  .demo-area {
    padding: 1rem;
  }

  .demo-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .stage-card,
  .loader-node {
    padding: 1rem;
  }

  .code-block,
  .output-block {
    font-size: 0.65rem;
    padding: 0.5rem;
  }

  .instruction-item {
    grid-template-columns: 25px 70px 1fr;
    font-size: 0.7rem;
  }
}
</style>
