<template>
  <div class="java-chapter4">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <h1 class="chapter-title">第四章：Class Files & Bytecode</h1>
          <p class="chapter-subtitle">深入JVM底层：类加载、字节码与反射机制</p>
          <div class="chapter-badge">
            <span class="badge-text">Under the Hood</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
            <span class="progress-text">{{ progress }}% 完成</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: 类加载与类对象 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="类加载与类对象 (Class Loading and Class Objects)"
                :concept-data="classLoadingData"
                @interaction="handleInteraction"
              >
                <div class="class-loading-showcase">
                  <h3>🚀 从.class文件到"鲜活"的类型</h3>

                  <div class="journey-overview">
                    <div class="journey-card">
                      <div class="journey-header">
                        <span class="journey-icon">📄</span>
                        <h4>起点：.class文件</h4>
                      </div>
                      <p>编译器生成的二进制文件，包含类的完整描述信息</p>
                    </div>

                    <div class="journey-arrow">→</div>

                    <div class="journey-card">
                      <div class="journey-header">
                        <span class="journey-icon">⚙️</span>
                        <h4>过程：类加载三部曲</h4>
                      </div>
                      <p>加载 → 链接（验证、准备、解析）→ 初始化</p>
                    </div>

                    <div class="journey-arrow">→</div>

                    <div class="journey-card">
                      <div class="journey-header">
                        <span class="journey-icon">🎯</span>
                        <h4>终点：Class对象</h4>
                      </div>
                      <p>堆中的java.lang.Class实例，反射的入口</p>
                    </div>
                  </div>

                  <div class="loading-phases">
                    <h4>🔄 类加载详细阶段</h4>
                    <div class="phases-grid">
                      <div v-for="(phase, index) in loadingPhases" :key="index" class="phase-card">
                        <div class="phase-header">
                          <span class="phase-number">{{ index + 1 }}</span>
                          <h5>{{ phase.name }}</h5>
                        </div>
                        <div class="phase-content">
                          <p class="phase-description">{{ phase.description }}</p>
                          <div class="phase-details">
                            <h6>关键活动：</h6>
                            <ul>
                              <li v-for="activity in phase.activities" :key="activity">
                                {{ activity }}
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="error-comparison">
                    <h4>🚨 常见错误对比</h4>
                    <div class="error-grid">
                      <div class="error-card cnf">
                        <h5>ClassNotFoundException</h5>
                        <div class="error-stage">加载阶段错误</div>
                        <p>类加载器在指定路径找不到.class文件</p>
                        <div class="error-example">
                          <strong>示例：</strong>
                          <code>Class.forName("com.mysql.jdbc.Driver")</code>
                          <span>但classpath中没有MySQL JAR包</span>
                        </div>
                      </div>
                      <div class="error-card ncd">
                        <h5>NoClassDefFoundError</h5>
                        <div class="error-stage">初始化阶段错误</div>
                        <p>类加载成功但初始化失败，再次使用时抛出</p>
                        <div class="error-example">
                          <strong>示例：</strong>
                          <code>静态代码块中抛出异常</code>
                          <span>导致类初始化失败</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 类加载器 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="类加载器 (Class Loaders)"
                :concept-data="classLoadersData"
                @interaction="handleInteraction"
              >
                <div class="classloaders-showcase">
                  <h3>🏗️ 类加载器：Java动态性的基石</h3>

                  <div class="delegation-model">
                    <h4>👨‍👩‍👧‍👦 双亲委派模型</h4>
                    <div class="model-explanation">
                      <div class="explanation-text">
                        <p>
                          <strong>核心思想：</strong
                          >子加载器收到加载请求时，先委派给父加载器尝试加载，只有父加载器无法加载时，子加载器才自己动手。
                        </p>
                        <p>
                          <strong>好处：</strong
                          >确保核心Java类的安全性，避免重复加载，维护类的唯一性。
                        </p>
                      </div>
                      <div class="delegation-hierarchy">
                        <div class="loader-level bootstrap">
                          <div class="loader-box">
                            <span class="loader-icon">🏛️</span>
                            <div class="loader-info">
                              <h6>Bootstrap ClassLoader</h6>
                              <p>C++实现，加载核心Java类库</p>
                              <div class="loader-scope">java.base模块</div>
                            </div>
                          </div>
                        </div>
                        <div class="delegation-arrow">⬇️ 委派</div>
                        <div class="loader-level platform">
                          <div class="loader-box">
                            <span class="loader-icon">🏢</span>
                            <div class="loader-info">
                              <h6>Platform ClassLoader</h6>
                              <p>加载JDK扩展库</p>
                              <div class="loader-scope">平台模块</div>
                            </div>
                          </div>
                        </div>
                        <div class="delegation-arrow">⬇️ 委派</div>
                        <div class="loader-level application">
                          <div class="loader-box">
                            <span class="loader-icon">📱</span>
                            <div class="loader-info">
                              <h6>Application ClassLoader</h6>
                              <p>加载用户类路径上的类</p>
                              <div class="loader-scope">Classpath / Module Path</div>
                            </div>
                          </div>
                        </div>
                        <div class="delegation-arrow">⬇️ 委派</div>
                        <div class="loader-level custom">
                          <div class="loader-box">
                            <span class="loader-icon">🔧</span>
                            <div class="loader-info">
                              <h6>Custom ClassLoader</h6>
                              <p>自定义加载逻辑</p>
                              <div class="loader-scope">特殊需求</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="isolation-demo">
                    <h4>🔒 命名空间隔离</h4>
                    <div class="isolation-explanation">
                      <div class="isolation-principle">
                        <h5>隔离原理</h5>
                        <p>
                          即使是相同全限定名的类，由不同类加载器实例加载后，在JVM中被视为完全不同的类型。
                        </p>
                        <div class="isolation-code">
                          <pre class="code-block">
// 同一个类，不同加载器
ClassLoader loader1 = new CustomClassLoader();
ClassLoader loader2 = new CustomClassLoader();

Class<?> class1 = loader1.loadClass("com.example.MyClass");
Class<?> class2 = loader2.loadClass("com.example.MyClass");

// 结果：class1 != class2
// instanceof 也会返回 false</pre
                          >
                        </div>
                      </div>
                      <div class="isolation-applications">
                        <h5>实际应用</h5>
                        <div class="application-examples">
                          <div class="app-example">
                            <span class="app-icon">🌐</span>
                            <div>
                              <h6>Web容器隔离</h6>
                              <p>Tomcat为每个Web应用创建独立的类加载器</p>
                            </div>
                          </div>
                          <div class="app-example">
                            <span class="app-icon">🔄</span>
                            <div>
                              <h6>热部署</h6>
                              <p>通过替换类加载器实现代码的动态更新</p>
                            </div>
                          </div>
                          <div class="app-example">
                            <span class="app-icon">🔌</span>
                            <div>
                              <h6>插件系统</h6>
                              <p>为每个插件提供独立的类加载环境</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: 剖析Class文件 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="剖析Class文件 (Analyzing Class Files)"
                :concept-data="classFileData"
                @interaction="handleInteraction"
              >
                <div class="classfile-showcase">
                  <h3>🔍 javap：Class文件的X光机</h3>

                  <div class="javap-introduction">
                    <div class="tool-overview">
                      <div class="tool-header">
                        <span class="tool-icon">🛠️</span>
                        <h4>javap工具</h4>
                      </div>
                      <p>JDK自带的类文件反汇编器，能够将二进制的.class文件转换为人类可读的格式。</p>
                    </div>

                    <div class="javap-options">
                      <h4>🎛️ 常用选项</h4>
                      <div class="options-grid">
                        <div class="option-item">
                          <code class="option-flag">-c</code>
                          <span class="option-desc">显示字节码指令</span>
                        </div>
                        <div class="option-item">
                          <code class="option-flag">-v</code>
                          <span class="option-desc">显示详细信息（常量池等）</span>
                        </div>
                        <div class="option-item">
                          <code class="option-flag">-p</code>
                          <span class="option-desc">显示所有成员（包括private）</span>
                        </div>
                        <div class="option-item">
                          <code class="option-flag">-s</code>
                          <span class="option-desc">显示内部类型签名</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="type-descriptors">
                    <h4>📝 类型描述符</h4>
                    <div class="descriptors-explanation">
                      <p>JVM内部使用紧凑的描述符来表示类型，这是理解字节码的基础。</p>
                      <div class="descriptors-table">
                        <div class="descriptor-category">
                          <h5>基本类型</h5>
                          <div class="descriptor-items">
                            <div class="descriptor-item"><code>I</code> → <span>int</span></div>
                            <div class="descriptor-item"><code>J</code> → <span>long</span></div>
                            <div class="descriptor-item"><code>D</code> → <span>double</span></div>
                            <div class="descriptor-item"><code>Z</code> → <span>boolean</span></div>
                            <div class="descriptor-item"><code>V</code> → <span>void</span></div>
                          </div>
                        </div>
                        <div class="descriptor-category">
                          <h5>引用类型</h5>
                          <div class="descriptor-items">
                            <div class="descriptor-item">
                              <code>Ljava/lang/String;</code> → <span>String</span>
                            </div>
                            <div class="descriptor-item"><code>[I</code> → <span>int[]</span></div>
                            <div class="descriptor-item">
                              <code>[[Ljava/lang/String;</code> → <span>String[][]</span>
                            </div>
                          </div>
                        </div>
                        <div class="descriptor-category">
                          <h5>方法描述符</h5>
                          <div class="descriptor-items">
                            <div class="descriptor-item">
                              <code>(Ljava/lang/String;I)V</code>
                              <span>void method(String s, int i)</span>
                            </div>
                            <div class="descriptor-item">
                              <code>()Ljava/lang/Object;</code>
                              <span>Object method()</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="constant-pool">
                    <h4>🗂️ 常量池：Class文件的心脏</h4>
                    <div class="pool-explanation">
                      <div class="pool-concept">
                        <h5>设计理念</h5>
                        <p>
                          常量池就像一个巨大的索引表，存储类中用到的所有字面量和符号引用。字节码指令不直接包含这些信息，而是通过索引来引用。
                        </p>
                      </div>
                      <div class="pool-structure">
                        <h5>结构示例</h5>
                        <div class="pool-example">
                          <pre class="code-block">
#1 = Class              #2             // MyClass
#2 = Utf8               MyClass
#3 = Fieldref          #1.#4          // MyClass.value:I
#4 = NameAndType       #5:#6          // value:I
#5 = Utf8               value
#6 = Utf8               I</pre
                          >
                        </div>
                        <div class="pool-explanation-text">
                          <p>通过这种层层索引的方式，JVM可以精确定位到任何类、字段或方法。</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: JVM字节码 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="JVM字节码 (JVM Bytecode)"
                :concept-data="bytecodeData"
                @interaction="handleInteraction"
              >
                <div class="bytecode-showcase">
                  <h3>⚙️ JVM字节码：虚拟机的机器语言</h3>

                  <div class="stack-architecture">
                    <h4>📚 基于栈的架构</h4>
                    <div class="architecture-explanation">
                      <div class="arch-concept">
                        <h5>设计理念</h5>
                        <p>
                          与基于寄存器的物理CPU不同，JVM采用基于栈的架构。所有计算都在操作数栈上进行，这使得指令集更加紧凑，跨平台移植更容易。
                        </p>
                      </div>
                      <div class="stack-demo">
                        <h5>执行示例：1 + 2</h5>
                        <div class="execution-steps">
                          <div class="step">
                            <div class="instruction">iconst_1</div>
                            <div class="stack-state">栈：[1]</div>
                            <div class="description">将常量1压入栈顶</div>
                          </div>
                          <div class="step-arrow">↓</div>
                          <div class="step">
                            <div class="instruction">iconst_2</div>
                            <div class="stack-state">栈：[1, 2]</div>
                            <div class="description">将常量2压入栈顶</div>
                          </div>
                          <div class="step-arrow">↓</div>
                          <div class="step">
                            <div class="instruction">iadd</div>
                            <div class="stack-state">栈：[3]</div>
                            <div class="description">弹出两个数相加，结果压入栈顶</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="instruction-categories">
                    <h4>🎯 指令分类</h4>
                    <div class="categories-grid">
                      <div class="category-card load-store">
                        <div class="category-header">
                          <span class="category-icon">📦</span>
                          <h5>加载/存储指令</h5>
                        </div>
                        <div class="category-content">
                          <p>在局部变量表和操作数栈之间移动数据</p>
                          <div class="instruction-examples">
                            <code>iload_0</code> <span>加载局部变量0到栈顶</span>
                            <code>astore_1</code> <span>将栈顶引用存入局部变量1</span>
                            <code>getfield</code> <span>获取实例字段值</span>
                            <code>putstatic</code> <span>设置静态字段值</span>
                          </div>
                        </div>
                      </div>

                      <div class="category-card arithmetic">
                        <div class="category-header">
                          <span class="category-icon">🧮</span>
                          <h5>运算指令</h5>
                        </div>
                        <div class="category-content">
                          <p>对操作数栈上的值进行算术和逻辑运算</p>
                          <div class="instruction-examples">
                            <code>iadd</code> <span>整数加法</span> <code>lsub</code>
                            <span>长整数减法</span> <code>fmul</code> <span>浮点数乘法</span>
                            <code>ddiv</code> <span>双精度除法</span>
                          </div>
                        </div>
                      </div>

                      <div class="category-card control">
                        <div class="category-header">
                          <span class="category-icon">🔀</span>
                          <h5>流程控制指令</h5>
                        </div>
                        <div class="category-content">
                          <p>控制程序执行流程的跳转和分支</p>
                          <div class="instruction-examples">
                            <code>if_icmpeq</code> <span>整数相等比较跳转</span> <code>goto</code>
                            <span>无条件跳转</span> <code>tableswitch</code>
                            <span>switch语句的表格跳转</span> <code>lookupswitch</code>
                            <span>switch语句的查找跳转</span>
                          </div>
                        </div>
                      </div>

                      <div class="category-card method-call">
                        <div class="category-header">
                          <span class="category-icon">📞</span>
                          <h5>方法调用指令</h5>
                        </div>
                        <div class="category-content">
                          <p>调用不同类型的方法</p>
                          <div class="instruction-examples">
                            <code>invokevirtual</code> <span>调用实例方法（支持多态）</span>
                            <code>invokespecial</code> <span>调用私有方法、构造器</span>
                            <code>invokestatic</code> <span>调用静态方法</span>
                            <code>invokeinterface</code> <span>调用接口方法</span>
                            <code>invokedynamic</code> <span>动态方法调用（Lambda等）</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="invoke-dynamic">
                    <h4>🚀 invokedynamic：现代Java的基石</h4>
                    <div class="dynamic-explanation">
                      <div class="dynamic-concept">
                        <p>
                          <strong>invokedynamic</strong>是Java
                          7引入的革命性指令，它支持在运行时动态确定调用目标。这是实现Lambda表达式、方法引用等现代Java特性的关键技术。
                        </p>
                      </div>
                      <div class="dynamic-applications">
                        <h5>应用场景</h5>
                        <div class="application-items">
                          <div class="app-item">
                            <span class="app-icon">λ</span>
                            <div>
                              <h6>Lambda表达式</h6>
                              <p>将Lambda转换为高效的方法调用</p>
                            </div>
                          </div>
                          <div class="app-item">
                            <span class="app-icon">🔗</span>
                            <div>
                              <h6>方法引用</h6>
                              <p>支持::语法的方法引用</p>
                            </div>
                          </div>
                          <div class="app-item">
                            <span class="app-icon">🌐</span>
                            <div>
                              <h6>动态语言支持</h6>
                              <p>为JVM上的动态语言提供基础</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 5: 反射机制 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ExpandableSection
                title="反射机制 (Reflection)"
                :concept-data="reflectionData"
                @interaction="handleInteraction"
              >
                <div class="reflection-showcase">
                  <h3>🪞 反射：Java的"魔镜"</h3>

                  <div class="reflection-concept">
                    <div class="concept-overview">
                      <div class="concept-card">
                        <div class="concept-header">
                          <span class="concept-icon">🔍</span>
                          <h4>什么是反射？</h4>
                        </div>
                        <p>
                          反射是程序在运行时检查和操作自身结构的能力。通过反射，你可以在运行时获取任意类的信息，创建对象，调用方法，访问字段。
                        </p>
                      </div>
                      <div class="concept-card">
                        <div class="concept-header">
                          <span class="concept-icon">🎯</span>
                          <h4>为什么需要反射？</h4>
                        </div>
                        <p>
                          反射使得框架能够处理未知的用户类。ORM框架、DI容器、序列化库等都大量使用反射来实现通用性和灵活性。
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="reflection-api">
                    <h4>🔧 核心API</h4>
                    <div class="api-flow">
                      <div class="api-step">
                        <div class="step-header">
                          <span class="step-number">1</span>
                          <h5>获取Class对象</h5>
                        </div>
                        <div class="step-content">
                          <div class="code-examples">
                            <code>Class.forName("com.example.MyClass")</code>
                            <code>MyClass.class</code>
                            <code>obj.getClass()</code>
                          </div>
                        </div>
                      </div>
                      <div class="flow-arrow">→</div>
                      <div class="api-step">
                        <div class="step-header">
                          <span class="step-number">2</span>
                          <h5>获取元数据</h5>
                        </div>
                        <div class="step-content">
                          <div class="metadata-types">
                            <div class="metadata-item">
                              <code>getMethod()</code>
                              <span>获取Method对象</span>
                            </div>
                            <div class="metadata-item">
                              <code>getField()</code>
                              <span>获取Field对象</span>
                            </div>
                            <div class="metadata-item">
                              <code>getConstructor()</code>
                              <span>获取Constructor对象</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="flow-arrow">→</div>
                      <div class="api-step">
                        <div class="step-header">
                          <span class="step-number">3</span>
                          <h5>动态操作</h5>
                        </div>
                        <div class="step-content">
                          <div class="operation-types">
                            <div class="operation-item">
                              <code>method.invoke()</code>
                              <span>调用方法</span>
                            </div>
                            <div class="operation-item">
                              <code>field.get/set()</code>
                              <span>访问字段</span>
                            </div>
                            <div class="operation-item">
                              <code>constructor.newInstance()</code>
                              <span>创建实例</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="reflection-internals">
                    <h4>⚙️ 内部实现机制</h4>
                    <div class="internals-explanation">
                      <div class="inflation-mechanism">
                        <h5>膨胀机制 (Inflation)</h5>
                        <div class="inflation-process">
                          <div class="inflation-stage">
                            <div class="stage-title">初始阶段</div>
                            <div class="stage-content">
                              <p><strong>Native Accessor</strong></p>
                              <p>使用本地代码实现，启动快但性能较低</p>
                            </div>
                          </div>
                          <div class="inflation-arrow">
                            <span>调用次数达到阈值</span>
                            <div class="arrow">→</div>
                          </div>
                          <div class="inflation-stage">
                            <div class="stage-title">优化阶段</div>
                            <div class="stage-content">
                              <p><strong>Bytecode Accessor</strong></p>
                              <p>动态生成字节码，可被JIT优化</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="reflection-tradeoffs">
                    <h4>⚖️ 反射的权衡</h4>
                    <div class="tradeoffs-grid">
                      <div class="tradeoff-card benefits">
                        <div class="tradeoff-header">
                          <span class="tradeoff-icon">✅</span>
                          <h5>优势</h5>
                        </div>
                        <ul>
                          <li>极大的灵活性和通用性</li>
                          <li>支持框架的通用设计</li>
                          <li>运行时动态行为</li>
                          <li>突破访问限制</li>
                        </ul>
                      </div>
                      <div class="tradeoff-card drawbacks">
                        <div class="tradeoff-header">
                          <span class="tradeoff-icon">❌</span>
                          <h5>代价</h5>
                        </div>
                        <ul>
                          <li>性能开销显著</li>
                          <li>破坏封装性</li>
                          <li>类型安全问题</li>
                          <li>代码可读性下降</li>
                        </ul>
                      </div>
                    </div>
                    <div class="best-practices">
                      <h5>💡 最佳实践</h5>
                      <p>
                        反射应该主要用于构建通用框架和工具。在业务代码中，如果发现需要使用反射，通常意味着设计上可能存在问题，应该考虑重构。
                      </p>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- 字节码与类文件演示 -->
            <section class="topic-section">
              <ExpandableSection
                title="🔧 字节码与类文件互动演示"
                :concept-data="{ keyPoints: ['体验类加载过程', '模拟字节码执行', '探索反射机制'] }"
                @interaction="handleInteraction"
              >
                <BytecodeDemo />
              </ExpandableSection>
            </section>

            <!-- 章节总结与思维导图 -->
            <section id="topic-5" class="topic-section chapter-summary" ref="topic5">
              <ExpandableSection
                title="📊 章节总结与知识体系图"
                :concept-data="chapterSummaryData"
                @interaction="handleInteraction"
              >
                <div class="summary-content">
                  <h3>🎯 本章核心收获</h3>
                  <div class="key-takeaways">
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🚀</span>
                      <div>
                        <h4>类加载机制</h4>
                        <p>理解从.class文件到Class对象的完整过程</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🏗️</span>
                      <div>
                        <h4>类加载器体系</h4>
                        <p>掌握双亲委派模型和命名空间隔离</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🔍</span>
                      <div>
                        <h4>Class文件结构</h4>
                        <p>学会使用javap分析字节码和常量池</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">⚙️</span>
                      <div>
                        <h4>字节码指令</h4>
                        <p>理解基于栈的执行模型和指令分类</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🪞</span>
                      <div>
                        <h4>反射机制</h4>
                        <p>掌握动态类型操作的原理和应用场景</p>
                      </div>
                    </div>
                  </div>

                  <div class="mindmap-container">
                    <h3>🧠 Class Files & Bytecode 知识体系图</h3>
                    <div class="mindmap-wrapper">
                      <div id="chapter4-mindmap" class="mermaid-container"></div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import BytecodeDemo from '@/components/BytecodeDemo.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: '类加载与类对象',
    description: '从.class文件到Class对象的完整旅程',
  },
  {
    title: '类加载器',
    description: '双亲委派模型与命名空间隔离',
  },
  {
    title: '剖析Class文件',
    description: 'javap工具与常量池结构',
  },
  {
    title: 'JVM字节码',
    description: '基于栈的指令集与执行模型',
  },
  {
    title: '反射机制',
    description: '运行时类型信息与动态调用',
  },
]

// 数据定义
const classLoadingData = {
  keyPoints: [
    '加载：读取.class文件，创建Class对象',
    '链接：验证、准备、解析三个子阶段',
    '初始化：执行<clinit>()方法',
    'Class对象：反射机制的入口点',
    '错误类型：ClassNotFoundException vs NoClassDefFoundError',
  ],
}

const classLoadersData = {
  keyPoints: [
    '双亲委派模型：先委派父加载器',
    '层级结构：Bootstrap → Platform → Application → Custom',
    '命名空间隔离：不同加载器的类互不相同',
    '安全性：防止核心类被恶意替换',
    '应用场景：Web容器、热部署、插件系统',
  ],
}

const classFileData = {
  keyPoints: [
    'javap工具：类文件反汇编器',
    '类型描述符：JVM内部类型表示',
    '常量池：字面量和符号引用的索引表',
    '方法签名：参数和返回类型的编码',
    '字节码指令：通过索引引用常量池',
  ],
}

const bytecodeData = {
  keyPoints: [
    '基于栈的架构：操作数栈 + 局部变量表',
    '指令分类：加载/存储、运算、流程控制、方法调用',
    '方法调用：invokevirtual、invokespecial、invokestatic、invokeinterface',
    '动态调用：invokedynamic支持Lambda等现代特性',
    'JIT编译：字节码到机器码的优化',
  ],
}

const reflectionData = {
  keyPoints: [
    'Class对象：反射的入口点',
    '核心API：Method、Field、Constructor',
    '动态调用：invoke()方法执行',
    '内部实现：Native Accessor → Bytecode Accessor',
    '性能代价：绕过编译时优化，运行时类型检查',
  ],
}

// 类加载阶段
const loadingPhases = [
  {
    name: '加载 (Loading)',
    description: '通过类的全限定名获取二进制字节流',
    activities: [
      '读取.class文件的二进制数据',
      '在方法区创建类的内部数据结构',
      '在堆中生成java.lang.Class对象',
      '建立Class对象与方法区数据的关联',
    ],
  },
  {
    name: '验证 (Verification)',
    description: '确保字节码符合JVM规范且安全',
    activities: [
      '文件格式验证：魔数、版本号等',
      '元数据验证：语义分析',
      '字节码验证：数据流和控制流分析',
      '符号引用验证：匹配性校验',
    ],
  },
  {
    name: '准备 (Preparation)',
    description: '为静态变量分配内存并设置零值',
    activities: [
      '为类的静态变量分配内存空间',
      '设置变量的初始零值（不是程序中的值）',
      '不执行任何Java代码',
      '不包括实例变量的处理',
    ],
  },
  {
    name: '解析 (Resolution)',
    description: '将符号引用转换为直接引用',
    activities: ['类或接口的解析', '字段解析', '方法解析', '接口方法解析'],
  },
  {
    name: '初始化 (Initialization)',
    description: '执行类构造器<clinit>()方法',
    activities: [
      '执行静态变量的赋值语句',
      '执行静态代码块',
      '保证父类先于子类初始化',
      '线程安全的执行过程',
    ],
  },
]

// 方法
const scrollToTopic = (index: number) => {
  currentTopic.value = index

  // 滚动到对应的主题部分
  const targetElement = document.getElementById(`topic-${index}`)
  if (targetElement) {
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('Interaction:', type)
}

const chapterSummaryData = {
  keyPoints: [
    '掌握JVM底层运行机制',
    '理解类加载和字节码执行',
    '学会分析Class文件结构',
    '了解反射的原理和应用',
  ],
}

// 初始化Mermaid
onMounted(async () => {
  try {
    console.log('开始初始化 Mermaid...')
    const mermaid = await import('mermaid')
    console.log('Mermaid 模块加载成功:', mermaid)

    mermaid.default.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
    })
    console.log('Mermaid 初始化完成')

    // 延迟渲染以确保DOM已加载
    setTimeout(async () => {
      try {
        console.log('开始渲染 Mermaid 图表...')

        // 创建思维导图内容
        const mindmapContent = `mindmap
  root((Class Files & Bytecode))
    类加载过程
      加载Loading
        读取class文件
        创建Class对象
      链接Linking
        验证Verification
        准备Preparation
        解析Resolution
      初始化Initialization
        执行clinit方法
    类加载器
      双亲委派模型
        Bootstrap
        Platform
        Application
        Custom
      命名空间隔离
        安全性保证
        应用隔离
    Class文件结构
      javap工具
        反汇编器
        分析字节码
      常量池
        符号引用
        字面量
      类型描述符
        基本类型
        引用类型
    JVM字节码
      基于栈架构
        操作数栈
        局部变量表
      指令分类
        加载存储
        运算指令
        流程控制
        方法调用
      invokedynamic
        Lambda支持
        动态语言
    反射机制
      Class对象
        反射入口
        元数据访问
      核心API
        Method
        Field
        Constructor
      内部实现
        膨胀机制
        性能优化`

        const container = document.getElementById('chapter4-mindmap')
        if (container) {
          console.log('找到容器，开始渲染...')
          const { svg } = await mermaid.default.render('chapter4-mindmap-svg', mindmapContent)
          container.innerHTML = svg
          console.log('Mermaid 图表渲染完成')
        } else {
          console.error('未找到思维导图容器')
        }
      } catch (renderError) {
        console.error('Mermaid 渲染错误:', renderError)
      }
    }, 1000) // 增加延迟时间
  } catch (error) {
    console.error('Mermaid 初始化失败:', error)
  }
})
</script>

<style scoped>
/* 基础样式 */
.java-chapter4 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.chapter-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 3rem 0;
  text-align: center;
  position: relative;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-content {
  position: relative;
  z-index: 1;
}

.chapter-title {
  font-size: 2.5rem;
  margin: 0 0 1rem 0;
  font-weight: 700;
}

.chapter-subtitle {
  font-size: 1.2rem;
  margin: 0 0 1rem 0;
  opacity: 0.9;
}

.chapter-badge {
  margin: 1rem 0 2rem 0;
}

.badge-text {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.progress-bar {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  height: 8px;
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.progress-fill {
  background: white;
  height: 100%;
  border-radius: 25px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -30px;
  right: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

.sidebar {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.outline {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.outline h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
}

.outline-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.5rem;
}

.outline-item:hover {
  background: #f8f9fa;
}

.outline-item.active {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.outline-number {
  width: 30px;
  height: 30px;
  background: #2c3e50;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.outline-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.outline-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tool-button {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.tool-button:hover {
  background: #f8f9fa;
  border-color: #2c3e50;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.topic-section {
  margin-bottom: 2rem;
}

.topic-section:last-child {
  margin-bottom: 0;
}
</style>

<style src="../styles/chapter4.css" scoped></style>
