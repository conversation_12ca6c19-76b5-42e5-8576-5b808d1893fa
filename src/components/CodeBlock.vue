<template>
  <div class="code-block">
    <pre><code :class="`language-${language}`" v-html="highlightedCode"></code></pre>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import hljs from 'highlight.js/lib/core'
import java from 'highlight.js/lib/languages/java'
import bash from 'highlight.js/lib/languages/bash'
import 'highlight.js/styles/atom-one-light.css'

hljs.registerLanguage('java', java)
hljs.registerLanguage('bash', bash)

const props = defineProps<{
  code: string
  language: 'java' | 'bash'
}>()

const highlightedCode = computed(() => {
  return hljs.highlight(props.code, { language: props.language }).value
})
</script>

<style scoped>
.code-block {
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
pre {
  margin: 0;
}
/* Override default hljs background */
pre code.hljs {
  padding: 1.5em;
  font-size: 1em;
}
</style>
