<template>
  <div class="compilation-animation">
    <div class="animation-header">
      <h3>☕ Java代码的"烹饪"之旅</h3>
      <p>看看你的Java代码是如何从"生米"变成"熟饭"的！</p>
    </div>

    <div class="animation-content">
      <!-- SVG动画区域 -->
      <div class="svg-container">
        <svg viewBox="0 0 800 400" class="compilation-svg">
          <!-- 背景 -->
          <rect width="800" height="400" fill="#f8f9fa" rx="10"/>
          
          <!-- Java源文件 -->
          <g id="source-file" :class="{ active: currentStep >= 0 }">
            <rect x="50" y="100" width="120" height="80" fill="#4CAF50" rx="10" opacity="0.8"/>
            <text x="110" y="130" text-anchor="middle" fill="white" font-weight="bold">📄</text>
            <text x="110" y="150" text-anchor="middle" fill="white" font-size="12">Main.java</text>
            <text x="110" y="165" text-anchor="middle" fill="white" font-size="10">源代码</text>
          </g>

          <!-- 编译器箭头 -->
          <g id="compiler-arrow" :class="{ active: currentStep >= 1 }">
            <path d="M 180 140 L 230 140" stroke="#FF9800" stroke-width="3" fill="none" 
                  marker-end="url(#arrowhead)" stroke-dasharray="5,5">
              <animate attributeName="stroke-dashoffset" values="10;0" dur="1s" 
                       repeatCount="indefinite" v-if="isPlaying && currentStep === 1"/>
            </path>
            <text x="205" y="130" text-anchor="middle" fill="#FF9800" font-size="12" font-weight="bold">javac</text>
          </g>

          <!-- 字节码文件 -->
          <g id="bytecode-file" :class="{ active: currentStep >= 2 }">
            <rect x="250" y="100" width="120" height="80" fill="#2196F3" rx="10" opacity="0.8"/>
            <text x="310" y="130" text-anchor="middle" fill="white" font-weight="bold">📦</text>
            <text x="310" y="150" text-anchor="middle" fill="white" font-size="12">Main.class</text>
            <text x="310" y="165" text-anchor="middle" fill="white" font-size="10">字节码</text>
          </g>

          <!-- JVM箭头 -->
          <g id="jvm-arrow" :class="{ active: currentStep >= 3 }">
            <path d="M 380 140 L 430 140" stroke="#9C27B0" stroke-width="3" fill="none" 
                  marker-end="url(#arrowhead)" stroke-dasharray="5,5">
              <animate attributeName="stroke-dashoffset" values="10;0" dur="1s" 
                       repeatCount="indefinite" v-if="isPlaying && currentStep === 3"/>
            </path>
            <text x="405" y="130" text-anchor="middle" fill="#9C27B0" font-size="12" font-weight="bold">JVM</text>
          </g>

          <!-- JVM内部处理 -->
          <g id="jvm-box" :class="{ active: currentStep >= 4 }">
            <rect x="450" y="50" width="300" height="300" fill="#E1F5FE" stroke="#0277BD" 
                  stroke-width="2" rx="15" opacity="0.9"/>
            <text x="600" y="40" text-anchor="middle" fill="#0277BD" font-weight="bold" font-size="14">
              🏭 JVM工厂
            </text>

            <!-- Class Loader -->
            <g id="class-loader" :class="{ active: currentStep >= 4 }">
              <rect x="470" y="80" width="100" height="50" fill="#FFC107" rx="8" opacity="0.8"/>
              <text x="520" y="105" text-anchor="middle" fill="white" font-size="10" font-weight="bold">
                📥 ClassLoader
              </text>
              <text x="520" y="118" text-anchor="middle" fill="white" font-size="8">类加载器</text>
            </g>

            <!-- Interpreter -->
            <g id="interpreter" :class="{ active: currentStep >= 5 }">
              <rect x="470" y="150" width="100" height="50" fill="#8BC34A" rx="8" opacity="0.8"/>
              <text x="520" y="175" text-anchor="middle" fill="white" font-size="10" font-weight="bold">
                🐌 Interpreter
              </text>
              <text x="520" y="188" text-anchor="middle" fill="white" font-size="8">解释器(慢)</text>
            </g>

            <!-- JIT Compiler -->
            <g id="jit-compiler" :class="{ active: currentStep >= 6 }">
              <rect x="470" y="220" width="100" height="50" fill="#F44336" rx="8" opacity="0.8"/>
              <text x="520" y="245" text-anchor="middle" fill="white" font-size="10" font-weight="bold">
                🚀 JIT Compiler
              </text>
              <text x="520" y="258" text-anchor="middle" fill="white" font-size="8">即时编译(快)</text>
            </g>

            <!-- 热点代码检测 -->
            <g id="hotspot-detection" :class="{ active: currentStep >= 6 }">
              <circle cx="630" cy="175" r="25" fill="#FF5722" opacity="0.8" v-if="currentStep >= 6">
                <animate attributeName="r" values="20;30;20" dur="1.5s" repeatCount="indefinite" 
                         v-if="isPlaying && currentStep === 6"/>
              </circle>
              <text x="630" y="175" text-anchor="middle" fill="white" font-size="10" font-weight="bold">
                🔥 热点
              </text>
              <text x="630" y="185" text-anchor="middle" fill="white" font-size="8">代码</text>
            </g>

            <!-- 机器码输出 -->
            <g id="machine-code" :class="{ active: currentStep >= 7 }">
              <rect x="600" y="280" width="120" height="50" fill="#795548" rx="8" opacity="0.8"/>
              <text x="660" y="305" text-anchor="middle" fill="white" font-size="10" font-weight="bold">
                ⚡ 机器码
              </text>
              <text x="660" y="318" text-anchor="middle" fill="white" font-size="8">原生执行</text>
            </g>
          </g>

          <!-- 箭头标记定义 -->
          <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                    refX="10" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
            </marker>
          </defs>
        </svg>
      </div>

      <!-- 控制面板 -->
      <div class="controls">
        <button @click="play" :disabled="isPlaying" class="control-btn play-btn">
          ▶️ 播放全程
        </button>
        <button @click="pause" :disabled="!isPlaying" class="control-btn pause-btn">
          ⏸️ 暂停
        </button>
        <button @click="prevStep" :disabled="currentStep <= 0" class="control-btn">
          ⏮️ 上一步
        </button>
        <button @click="nextStep" :disabled="currentStep >= steps.length - 1" class="control-btn">
          ⏭️ 下一步
        </button>
        <button @click="reset" class="control-btn reset-btn">
          🔄 重置
        </button>
      </div>

      <!-- 步骤说明 -->
      <div class="step-explanation">
        <div class="step-counter">
          第 {{ currentStep + 1 }} 步 / 共 {{ steps.length }} 步
        </div>
        <div class="step-title" :key="currentStep">
          {{ steps[currentStep]?.title || '准备开始' }}
        </div>
        <div class="step-description" :key="currentStep">
          {{ steps[currentStep]?.description || '点击"播放全程"或"下一步"开始学习Java编译过程' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface Step {
  title: string
  description: string
}

const currentStep = ref(-1)
const isPlaying = ref(false)
const playInterval = ref<NodeJS.Timeout | null>(null)

const steps: Step[] = [
  {
    title: '🍚 生米阶段：Java源代码',
    description: '你写的Java代码就像厨师手里的"生米"，人类能看懂，但计算机还不能直接"消化"。这时候就需要"大厨"javac出场了！'
  },
  {
    title: '👨‍🍳 大厨出手：javac编译器',
    description: 'javac编译器就像一位经验丰富的大厨，他把你的"生米"Java代码按照标准食谱翻译成"半成品"——字节码。这个过程叫做编译。'
  },
  {
    title: '📦 半成品：字节码(.class文件)',
    description: '编译后的字节码就像标准化的"配菜包"，任何JVM都能理解。这就是Java"一次编写，到处运行"的秘密武器！'
  },
  {
    title: '🚚 配送服务：JVM启动',
    description: 'JVM就像一个超级智能的"厨房"，它接收字节码这个"配菜包"，准备把它变成可以直接享用的"美味佳肴"。'
  },
  {
    title: '📥 入库管理：类加载器',
    description: 'ClassLoader像仓库管理员，负责把.class文件"配菜包"从磁盘搬到内存中，并做好分类整理，准备"下锅"。'
  },
  {
    title: '🐌 学徒上菜：解释器执行',
    description: '解释器像个认真的学徒，一行一行地按照字节码"食谱"执行。虽然慢，但绝对不会出错。适合第一次执行的代码。'
  },
  {
    title: '🔥 大厨观察：热点代码检测',
    description: '当某段代码被反复执行很多次时，JIT编译器这位"主厨"就会注意到：这道菜很受欢迎！是时候优化制作流程了。'
  },
  {
    title: '⚡ 飞速制作：JIT编译优化',
    description: '对于热点代码，JIT编译器会将其直接编译成本地机器码，就像主厨熟练掌握了制作流程，飞速出菜。这就是Java越跑越快的原因！'
  }
]

const play = (): void => {
  if (currentStep.value >= steps.length - 1) {
    reset()
  }
  
  isPlaying.value = true
  playInterval.value = setInterval(() => {
    if (currentStep.value < steps.length - 1) {
      currentStep.value++
    } else {
      pause()
    }
  }, 2000)
}

const pause = (): void => {
  isPlaying.value = false
  if (playInterval.value) {
    clearInterval(playInterval.value)
    playInterval.value = null
  }
}

const nextStep = (): void => {
  if (currentStep.value < steps.length - 1) {
    currentStep.value++
  }
}

const prevStep = (): void => {
  if (currentStep.value > -1) {
    currentStep.value--
  }
}

const reset = (): void => {
  pause()
  currentStep.value = -1
}

onMounted(() => {
  console.log('Java编译动画组件已加载')
})

onUnmounted(() => {
  pause()
})
</script>

<style scoped>
.compilation-animation {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.animation-header {
  text-align: center;
  margin-bottom: 2rem;
}

.animation-header h3 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.animation-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.animation-content {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.svg-container {
  width: 100%;
  margin-bottom: 2rem;
  background: #f8f9fa;
  border-radius: 10px;
  padding: 1rem;
}

.compilation-svg {
  width: 100%;
  height: auto;
}

.compilation-svg g {
  opacity: 0.3;
  transition: opacity 0.5s ease;
}

.compilation-svg g.active {
  opacity: 1;
}

.controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.control-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.play-btn {
  background: #27ae60;
  color: white;
}

.play-btn:hover:not(:disabled) {
  background: #219a52;
  transform: translateY(-2px);
}

.pause-btn {
  background: #f39c12;
  color: white;
}

.pause-btn:hover:not(:disabled) {
  background: #e67e22;
  transform: translateY(-2px);
}

.reset-btn {
  background: #e74c3c;
  color: white;
}

.reset-btn:hover {
  background: #c0392b;
  transform: translateY(-2px);
}

.control-btn:not(.play-btn):not(.pause-btn):not(.reset-btn) {
  background: #3498db;
  color: white;
}

.control-btn:not(.play-btn):not(.pause-btn):not(.reset-btn):hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-2px);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.step-explanation {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
}

.step-counter {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 0.5rem;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  animation: fadeIn 0.5s ease;
}

.step-description {
  font-size: 1.1rem;
  line-height: 1.6;
  animation: fadeIn 0.5s ease 0.2s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .animation-content {
    padding: 1rem;
  }
  
  .controls {
    gap: 0.5rem;
  }
  
  .control-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  
  .step-title {
    font-size: 1.2rem;
  }
  
  .step-description {
    font-size: 1rem;
  }
}
</style> 