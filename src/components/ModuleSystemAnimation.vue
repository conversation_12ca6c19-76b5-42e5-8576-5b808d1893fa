<template>
  <div class="module-animation">
    <div class="animation-controls">
      <h3>📦 模块系统运行演示</h3>
      <p class="animation-description">{{ currentStep.description }}</p>
      <div class="control-buttons">
        <button @click="playAnimation" :disabled="isPlaying" class="play-btn">
          {{ isPlaying ? '⏸️ 暂停' : '▶️ 播放' }}
        </button>
        <button @click="resetAnimation" class="reset-btn">🔄 重置</button>
        <button @click="prevStep" :disabled="currentStepIndex === 0" class="step-btn">
          ⬅️ 上一步
        </button>
        <button
          @click="nextStep"
          :disabled="currentStepIndex === steps.length - 1"
          class="step-btn"
        >
          ➡️ 下一步
        </button>
      </div>
      <div class="step-indicator">步骤 {{ currentStepIndex + 1 }} / {{ steps.length }}</div>
    </div>

    <div class="animation-canvas">
      <svg width="800" height="600" viewBox="0 0 800 600">
        <!-- 背景网格 -->
        <defs>
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1" />
          </pattern>
        </defs>
        <rect width="800" height="600" fill="url(#grid)" opacity="0.3" />

        <!-- JVM 容器 -->
        <rect
          x="50"
          y="50"
          width="700"
          height="500"
          fill="#f8f9fa"
          stroke="#6c757d"
          stroke-width="2"
          rx="10"
          class="jvm-container"
        />
        <text x="400" y="80" text-anchor="middle" class="title-text">Java 虚拟机 (JVM)</text>

        <!-- 模块路径区域 -->
        <g class="module-path-area">
          <rect
            x="80"
            y="120"
            width="320"
            height="200"
            fill="#e3f2fd"
            stroke="#2196f3"
            stroke-width="2"
            rx="8"
            :class="{ highlight: currentStepIndex >= 1 }"
          />
          <text x="240" y="145" text-anchor="middle" class="section-title">
            模块路径 (Module Path)
          </text>

          <!-- 平台模块 -->
          <g class="platform-module" :class="{ 'animate-in': currentStepIndex >= 2 }">
            <rect
              x="100"
              y="160"
              width="80"
              height="60"
              fill="#4caf50"
              stroke="#388e3c"
              stroke-width="2"
              rx="5"
            />
            <text x="140" y="185" text-anchor="middle" class="module-text">java.base</text>
            <text x="140" y="200" text-anchor="middle" class="module-type">平台模块</text>
          </g>

          <!-- 应用模块 -->
          <g class="app-module" :class="{ 'animate-in': currentStepIndex >= 3 }">
            <rect
              x="200"
              y="160"
              width="80"
              height="60"
              fill="#ff9800"
              stroke="#f57c00"
              stroke-width="2"
              rx="5"
            />
            <text x="240" y="185" text-anchor="middle" class="module-text">my.app</text>
            <text x="240" y="200" text-anchor="middle" class="module-type">应用模块</text>
          </g>

          <!-- 自动模块 -->
          <g class="auto-module" :class="{ 'animate-in': currentStepIndex >= 4 }">
            <rect
              x="300"
              y="160"
              width="80"
              height="60"
              fill="#9c27b0"
              stroke="#7b1fa2"
              stroke-width="2"
              rx="5"
            />
            <text x="340" y="185" text-anchor="middle" class="module-text">old.jar</text>
            <text x="340" y="200" text-anchor="middle" class="module-type">自动模块</text>
          </g>

          <!-- 模块依赖箭头 -->
          <g class="dependencies" :class="{ show: currentStepIndex >= 5 }">
            <path
              d="M 180 190 L 200 190"
              stroke="#333"
              stroke-width="2"
              marker-end="url(#arrowhead)"
            />
            <path
              d="M 280 190 L 300 190"
              stroke="#333"
              stroke-width="2"
              marker-end="url(#arrowhead)"
            />
          </g>
        </g>

        <!-- 类路径区域 -->
        <g class="classpath-area">
          <rect
            x="420"
            y="120"
            width="300"
            height="120"
            fill="#fff3e0"
            stroke="#ff9800"
            stroke-width="2"
            rx="8"
            :class="{ highlight: currentStepIndex >= 1 }"
          />
          <text x="570" y="145" text-anchor="middle" class="section-title">类路径 (Classpath)</text>

          <!-- 未命名模块 -->
          <g class="unnamed-module" :class="{ 'animate-in': currentStepIndex >= 4 }">
            <rect
              x="440"
              y="160"
              width="260"
              height="60"
              fill="#ffcc80"
              stroke="#ff8f00"
              stroke-width="2"
              rx="5"
            />
            <text x="570" y="185" text-anchor="middle" class="module-text">
              未命名模块 (所有传统JAR)
            </text>
            <text x="570" y="200" text-anchor="middle" class="module-type">
              legacy1.jar, legacy2.jar...
            </text>
          </g>
        </g>

        <!-- 强封装演示区域 -->
        <g class="encapsulation-demo" :class="{ show: currentStepIndex >= 6 }">
          <rect
            x="80"
            y="350"
            width="640"
            height="180"
            fill="#f3e5f5"
            stroke="#9c27b0"
            stroke-width="2"
            rx="8"
          />
          <text x="400" y="375" text-anchor="middle" class="section-title">强封装机制演示</text>

          <!-- 模块A -->
          <g class="module-a">
            <rect
              x="120"
              y="390"
              width="120"
              height="120"
              fill="#e8f5e8"
              stroke="#4caf50"
              stroke-width="2"
              rx="5"
            />
            <text x="180" y="415" text-anchor="middle" class="module-name">模块 A</text>

            <!-- 导出的包 -->
            <rect
              x="130"
              y="430"
              width="100"
              height="25"
              fill="#4caf50"
              stroke="#388e3c"
              stroke-width="1"
              rx="3"
            />
            <text x="180" y="448" text-anchor="middle" class="export-text">exports com.a.api</text>

            <!-- 内部包 -->
            <rect
              x="130"
              y="465"
              width="100"
              height="25"
              fill="#ffcccc"
              stroke="#f44336"
              stroke-width="1"
              rx="3"
            />
            <text x="180" y="483" text-anchor="middle" class="internal-text">com.a.internal</text>
          </g>

          <!-- 模块B -->
          <g class="module-b">
            <rect
              x="280"
              y="390"
              width="120"
              height="120"
              fill="#e3f2fd"
              stroke="#2196f3"
              stroke-width="2"
              rx="5"
            />
            <text x="340" y="415" text-anchor="middle" class="module-name">模块 B</text>
            <text x="340" y="435" text-anchor="middle" class="requires-text">requires A</text>
          </g>

          <!-- 访问箭头 -->
          <g class="access-arrows" :class="{ animate: currentStepIndex >= 7 }">
            <!-- 成功访问 -->
            <path
              d="M 240 442 L 280 442"
              stroke="#4caf50"
              stroke-width="3"
              marker-end="url(#greenArrow)"
            />
            <text x="260" y="435" text-anchor="middle" class="access-label success">✓ 可访问</text>

            <!-- 禁止访问 -->
            <path
              d="M 240 477 L 280 477"
              stroke="#f44336"
              stroke-width="3"
              stroke-dasharray="5,5"
              marker-end="url(#redArrow)"
            />
            <text x="260" y="500" text-anchor="middle" class="access-label blocked">
              ✗ 禁止访问
            </text>
          </g>

          <!-- jlink演示 -->
          <g class="jlink-demo" :class="{ show: currentStepIndex >= 8 }">
            <rect
              x="450"
              y="390"
              width="240"
              height="120"
              fill="#fff8e1"
              stroke="#ffc107"
              stroke-width="2"
              rx="5"
            />
            <text x="570" y="415" text-anchor="middle" class="module-name">jlink 精简运行时</text>

            <text x="570" y="440" text-anchor="middle" class="jlink-text">传统 JRE: ~200MB</text>
            <text x="570" y="460" text-anchor="middle" class="jlink-text">精简后: ~30MB</text>
            <text x="570" y="480" text-anchor="middle" class="jlink-benefit">节省 85% 空间！</text>
          </g>
        </g>

        <!-- 箭头标记定义 -->
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
          </marker>
          <marker
            id="greenArrow"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50" />
          </marker>
          <marker id="redArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#f44336" />
          </marker>
        </defs>
      </svg>
    </div>

    <div class="step-explanation">
      <div class="explanation-card" :class="currentStep.type">
        <h4>{{ currentStep.title }}</h4>
        <p>{{ currentStep.detail }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'

interface AnimationStep {
  id: number
  title: string
  description: string
  detail: string
  type: 'info' | 'success' | 'warning' | 'danger'
}

const currentStepIndex = ref(0)
const isPlaying = ref(false)
let animationTimer: number | null = null

const steps: AnimationStep[] = [
  {
    id: 0,
    title: '模块系统初始化',
    description: 'JVM启动，准备加载模块',
    detail: '当JVM启动时，它会扫描模块路径和类路径，准备构建模块图。这是模块系统工作的第一步。',
    type: 'info',
  },
  {
    id: 1,
    title: '扫描模块和类路径',
    description: 'JVM识别出两个不同的加载区域',
    detail:
      '模块路径包含模块化的JAR文件，类路径包含传统的JAR文件。这两个区域有完全不同的加载规则。',
    type: 'info',
  },
  {
    id: 2,
    title: '加载平台模块',
    description: '首先加载JDK内置的平台模块',
    detail:
      'java.base是所有模块的基础，它会被自动加载。其他平台模块如java.sql、java.xml等按需加载。',
    type: 'success',
  },
  {
    id: 3,
    title: '加载应用模块',
    description: '加载用户定义的应用模块',
    detail: '应用模块有完整的module-info.java文件，明确声明了依赖关系和导出的包。',
    type: 'success',
  },
  {
    id: 4,
    title: '处理自动模块和未命名模块',
    description: '处理传统的非模块化JAR文件',
    detail: '放在模块路径上的传统JAR成为自动模块，放在类路径上的所有JAR归入未命名模块。',
    type: 'warning',
  },
  {
    id: 5,
    title: '构建依赖关系图',
    description: '分析并验证模块间的依赖关系',
    detail: 'JVM读取所有module-info文件，构建完整的依赖关系图，并进行循环依赖检查。',
    type: 'info',
  },
  {
    id: 6,
    title: '强封装机制生效',
    description: '展示模块的强封装如何工作',
    detail: '只有通过exports明确导出的包才能被其他模块访问，内部包被完全隐藏。',
    type: 'success',
  },
  {
    id: 7,
    title: '访问控制验证',
    description: '演示合法访问和禁止访问',
    detail: '模块B可以访问模块A导出的com.a.api包，但无法访问内部的com.a.internal包。',
    type: 'success',
  },
  {
    id: 8,
    title: 'jlink工具优化',
    description: '使用jlink创建精简运行时',
    detail: 'jlink工具可以分析依赖关系，只打包应用实际需要的模块，大大减少运行时体积。',
    type: 'success',
  },
]

const currentStep = computed(() => steps[currentStepIndex.value])

const playAnimation = () => {
  if (isPlaying.value) {
    pauseAnimation()
    return
  }

  isPlaying.value = true
  animationTimer = window.setInterval(() => {
    if (currentStepIndex.value < steps.length - 1) {
      currentStepIndex.value++
    } else {
      pauseAnimation()
    }
  }, 2000)
}

const pauseAnimation = () => {
  isPlaying.value = false
  if (animationTimer) {
    clearInterval(animationTimer)
    animationTimer = null
  }
}

const resetAnimation = () => {
  pauseAnimation()
  currentStepIndex.value = 0
}

const nextStep = () => {
  if (currentStepIndex.value < steps.length - 1) {
    currentStepIndex.value++
  }
}

const prevStep = () => {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--
  }
}

onMounted(() => {
  console.log('模块系统动画组件已加载')
})
</script>

<style scoped>
.module-animation {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem;
}

.animation-controls {
  text-align: center;
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.animation-controls h3 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.animation-description {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 1rem;
  font-weight: 500;
}

.control-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.control-buttons button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.play-btn {
  background: #4caf50;
  color: white;
}

.play-btn:hover:not(:disabled) {
  background: #45a049;
}

.reset-btn {
  background: #ff9800;
  color: white;
}

.reset-btn:hover {
  background: #f57c00;
}

.step-btn {
  background: #2196f3;
  color: white;
}

.step-btn:hover:not(:disabled) {
  background: #1976d2;
}

.step-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.step-indicator {
  font-size: 0.9rem;
  color: #666;
  font-weight: bold;
}

.animation-canvas {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.animation-canvas svg {
  width: 100%;
  height: auto;
  min-width: 800px;
}

/* SVG 样式 */
.title-text {
  font-size: 18px;
  font-weight: bold;
  fill: #2c3e50;
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  fill: #444;
}

.module-text {
  font-size: 11px;
  font-weight: bold;
  fill: white;
}

.module-type {
  font-size: 9px;
  fill: white;
}

.module-name {
  font-size: 12px;
  font-weight: bold;
  fill: #333;
}

.export-text,
.internal-text,
.requires-text {
  font-size: 9px;
  fill: white;
  font-weight: bold;
}

.access-label {
  font-size: 10px;
  font-weight: bold;
}

.access-label.success {
  fill: #4caf50;
}

.access-label.blocked {
  fill: #f44336;
}

.jlink-text {
  font-size: 11px;
  fill: #333;
}

.jlink-benefit {
  font-size: 12px;
  font-weight: bold;
  fill: #ff6f00;
}

/* 动画效果 */
.highlight {
  stroke: #ff6f00 !important;
  stroke-width: 3px !important;
  animation: pulse 2s infinite;
}

.animate-in {
  animation: slideIn 1s ease-out;
}

.show {
  opacity: 1;
  animation: fadeIn 1s ease-in;
}

.show:not(.show) {
  opacity: 0;
}

.animate {
  animation: bounce 1s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% {
    stroke-opacity: 1;
  }
  50% {
    stroke-opacity: 0.5;
  }
  100% {
    stroke-opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(5px);
  }
}

.step-explanation {
  margin-top: 1rem;
}

.explanation-card {
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.explanation-card.info {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-left: 4px solid #2196f3;
}

.explanation-card.success {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border-left: 4px solid #4caf50;
}

.explanation-card.warning {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
  border-left: 4px solid #ff9800;
}

.explanation-card.danger {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  border-left: 4px solid #f44336;
}

.explanation-card h4 {
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-size: 1.2rem;
}

.explanation-card p {
  color: #555;
  line-height: 1.6;
  margin: 0;
}

@media (max-width: 768px) {
  .control-buttons {
    flex-direction: column;
    align-items: center;
  }

  .control-buttons button {
    width: 200px;
  }
}
</style>
