<template>
  <div class="java-chapter2">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <h1 class="chapter-title">第二章：Java 模块系统 (JPMS)</h1>
          <p class="chapter-subtitle">深入理解 Project Jigsaw 与模块化架构</p>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
            <span class="progress-text">{{ progress }}% 完成</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: 模块化背景 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="模块化背景：为何需要模块系统？"
                :concept-data="moduleBackgroundData"
                @interaction="handleInteraction"
              >
                <div class="jar-hell-demo">
                  <h3>🔥 JAR Hell 问题演示</h3>
                  <div class="problem-showcase">
                    <div class="before-modules">
                      <h4>传统 Classpath 的问题</h4>
                      <div class="jar-conflicts">
                        <div class="jar-item conflict">
                          <span class="jar-name">log4j-1.2.jar</span>
                          <span class="conflict-badge">冲突!</span>
                        </div>
                        <div class="jar-item conflict">
                          <span class="jar-name">log4j-2.0.jar</span>
                          <span class="conflict-badge">冲突!</span>
                        </div>
                        <div class="jar-item">
                          <span class="jar-name">commons-lang.jar</span>
                        </div>
                        <div class="jar-item">
                          <span class="jar-name">spring-core.jar</span>
                        </div>
                      </div>
                      <div class="problem-description">
                        <p>❌ 版本冲突不可预测</p>
                        <p>❌ 依赖关系隐藏</p>
                        <p>❌ 内部API被滥用</p>
                      </div>
                    </div>

                    <div class="arrow-separator">→</div>

                    <div class="after-modules">
                      <h4>模块化解决方案</h4>
                      <div class="module-graph">
                        <div class="module-item platform">
                          <span class="module-name">java.base</span>
                          <span class="module-type">平台模块</span>
                        </div>
                        <div class="module-item application">
                          <span class="module-name">my.app</span>
                          <span class="module-type">应用模块</span>
                        </div>
                        <div class="module-item automatic">
                          <span class="module-name">commons.lang3</span>
                          <span class="module-type">自动模块</span>
                        </div>
                      </div>
                      <div class="solution-description">
                        <p>✅ 明确的依赖声明</p>
                        <p>✅ 强封装保护</p>
                        <p>✅ 启动时验证</p>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 模块基本语法 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="模块的基本语法 (module-info.java)"
                :concept-data="moduleInfoData"
                @interaction="handleInteraction"
              >
                <ModuleCodePlayground
                  :examples="moduleInfoExamples"
                  title="module-info.java 语法实践"
                  @code-run="handleCodeRun"
                />
              </ExpandableSection>
            </section>

            <!-- Topic 3: 模块加载与类型 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="模块的加载与类型"
                :concept-data="moduleTypesData"
                @interaction="handleInteraction"
              >
                <ModuleSystemAnimation />
                <div class="module-types-detail">
                  <h3>🏷️ 四种模块类型详解</h3>
                  <div class="types-grid">
                    <div class="type-card platform">
                      <div class="type-header">
                        <span class="type-icon">🏛️</span>
                        <h4>平台模块</h4>
                        <span class="type-badge">Platform</span>
                      </div>
                      <div class="type-content">
                        <p><strong>定义：</strong>JDK 内置的模块</p>
                        <p><strong>示例：</strong>java.base, java.sql, java.xml</p>
                        <p><strong>特点：</strong>完全模块化，强封装</p>
                        <div class="type-examples">
                          <code>java.base</code> - 所有模块的基础<br />
                          <code>java.sql</code> - 数据库访问<br />
                          <code>java.xml</code> - XML 处理
                        </div>
                      </div>
                    </div>

                    <div class="type-card application">
                      <div class="type-header">
                        <span class="type-icon">📱</span>
                        <h4>应用模块</h4>
                        <span class="type-badge">Application</span>
                      </div>
                      <div class="type-content">
                        <p><strong>定义：</strong>用户编写的标准模块</p>
                        <p><strong>要求：</strong>必须有 module-info.java</p>
                        <p><strong>特点：</strong>明确的依赖和导出声明</p>
                        <div class="type-examples">
                          完整的模块描述符<br />
                          清晰的 API 边界<br />
                          可靠的配置
                        </div>
                      </div>
                    </div>

                    <div class="type-card automatic">
                      <div class="type-header">
                        <span class="type-icon">🔄</span>
                        <h4>自动模块</h4>
                        <span class="type-badge">Automatic</span>
                      </div>
                      <div class="type-content">
                        <p><strong>定义：</strong>模块路径上的传统 JAR</p>
                        <p><strong>行为：</strong>导出所有包，读取所有模块</p>
                        <p><strong>用途：</strong>迁移过程中的过渡方案</p>
                        <div class="type-examples">
                          名称由 JAR 文件名推断<br />
                          最大兼容性策略<br />
                          临时解决方案
                        </div>
                      </div>
                    </div>

                    <div class="type-card unnamed">
                      <div class="type-header">
                        <span class="type-icon">❓</span>
                        <h4>未命名模块</h4>
                        <span class="type-badge">Unnamed</span>
                      </div>
                      <div class="type-content">
                        <p><strong>定义：</strong>类路径上的所有 JAR</p>
                        <p><strong>限制：</strong>具名模块不能依赖它</p>
                        <p><strong>特点：</strong>传统的 classpath 行为</p>
                        <div class="type-examples">
                          单一虚拟模块<br />
                          向后兼容<br />
                          逐步淘汰
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: 构建和运行 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="构建和运行模块化应用"
                :concept-data="buildRunData"
                @interaction="handleInteraction"
              >
                <div class="command-showcase">
                  <h3>🛠️ 模块化命令行工具</h3>
                  <div class="command-sections">
                    <div class="command-section">
                      <h4>编译模块</h4>
                      <div class="command-block">
                        <code>javac -d out --module-source-path src -m wgjd.discovery</code>
                      </div>
                      <p class="command-explanation">
                        使用 <code>--module-source-path</code> 指定模块源码路径，<code>-m</code>
                        指定要编译的模块
                      </p>
                    </div>

                    <div class="command-section">
                      <h4>运行模块</h4>
                      <div class="command-block">
                        <code>java --module-path out -m wgjd.discovery/wgjd.discovery.Main</code>
                      </div>
                      <p class="command-explanation">
                        使用 <code>--module-path</code> 指定模块路径，<code>-m</code>
                        指定主模块和主类
                      </p>
                    </div>

                    <div class="command-section">
                      <h4>反射访问控制</h4>
                      <div class="command-block">
                        <code>java --add-opens java.base/sun.net=my.framework</code>
                      </div>
                      <p class="command-explanation">
                        使用 <code>--add-opens</code> 允许特定模块通过反射访问内部包
                      </p>
                    </div>

                    <div class="command-section">
                      <h4>静态访问控制</h4>
                      <div class="command-block">
                        <code>java --add-exports java.base/sun.misc=my.module</code>
                      </div>
                      <p class="command-explanation">
                        使用 <code>--add-exports</code> 允许特定模块访问未导出的包
                      </p>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 5: 架构设计 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ExpandableSection
                title="模块化架构设计"
                :concept-data="architectureData"
                @interaction="handleInteraction"
              >
                <div class="architecture-patterns">
                  <h3>🏗️ 模块化设计模式</h3>

                  <!-- 分裂包问题 -->
                  <div class="pattern-detail">
                    <div class="pattern-header">
                      <span class="pattern-icon">💥</span>
                      <h4>分裂包 (Split Packages) 问题</h4>
                      <span class="severity-badge danger">严重问题</span>
                    </div>
                    <div class="pattern-content">
                      <div class="problem-demo">
                        <div class="split-package-example">
                          <div class="module-box">
                            <h5>模块 A</h5>
                            <div class="package-item">com.example.util.StringUtils</div>
                          </div>
                          <div class="conflict-indicator">❌</div>
                          <div class="module-box">
                            <h5>模块 B</h5>
                            <div class="package-item">com.example.util.DateUtils</div>
                          </div>
                        </div>
                        <p class="problem-explanation">
                          <strong>问题：</strong>同一个包
                          <code>com.example.util</code>
                          分散在两个模块中，这在模块系统中是严格禁止的！
                        </p>
                        <div class="solution-box">
                          <h6>解决方案：</h6>
                          <ul>
                            <li>重构：将分裂的包合并到一个模块中</li>
                            <li>重命名：修改其中一个包的名称</li>
                            <li>创建共享模块：将公共包提取到独立模块</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 多版本JAR -->
                  <div class="pattern-detail">
                    <div class="pattern-header">
                      <span class="pattern-icon">📦</span>
                      <h4>多版本 JAR (Multi-Release JAR)</h4>
                      <span class="severity-badge success">最佳实践</span>
                    </div>
                    <div class="pattern-content">
                      <div class="mr-jar-structure">
                        <h5>MR-JAR 文件结构</h5>
                        <div class="file-tree">
                          <div class="tree-item">📁 my-library.jar</div>
                          <div class="tree-item level-1">
                            📄 com/mycompany/MyClass.class <span class="version-tag">Java 8</span>
                          </div>
                          <div class="tree-item level-1">📁 META-INF/</div>
                          <div class="tree-item level-2">📁 versions/</div>
                          <div class="tree-item level-3">📁 11/</div>
                          <div class="tree-item level-4">
                            📄 com/mycompany/MyClass.class <span class="version-tag">Java 11</span>
                          </div>
                          <div class="tree-item level-3">📁 17/</div>
                          <div class="tree-item level-4">
                            📄 com/mycompany/MyClass.class <span class="version-tag">Java 17</span>
                          </div>
                        </div>
                        <p class="mr-jar-explanation">
                          高版本 JVM 会优先加载对应版本的实现，低版本 JVM
                          使用根目录的默认实现，实现完美的向后兼容。
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 6: jlink工具 -->
            <section id="topic-5" class="topic-section" ref="topic5">
              <ExpandableSection
                title="jlink：模块化的终极优势"
                :concept-data="jlinkData"
                @interaction="handleInteraction"
              >
                <div class="jlink-showcase">
                  <h3>🔗 jlink 工具演示</h3>

                  <div class="jlink-comparison">
                    <div class="traditional-deployment">
                      <h4>传统部署方式</h4>
                      <div class="deployment-stack">
                        <div class="layer jre-layer">
                          <span class="layer-name">完整 JRE</span>
                          <span class="layer-size">~200MB</span>
                        </div>
                        <div class="layer app-layer">
                          <span class="layer-name">应用程序</span>
                          <span class="layer-size">~50MB</span>
                        </div>
                      </div>
                      <div class="total-size traditional">总计: ~250MB</div>
                    </div>

                    <div class="jlink-deployment">
                      <h4>jlink 精简部署</h4>
                      <div class="deployment-stack">
                        <div class="layer custom-runtime">
                          <span class="layer-name">定制运行时</span>
                          <span class="layer-size">~30MB</span>
                        </div>
                        <div class="layer app-layer">
                          <span class="layer-name">应用程序</span>
                          <span class="layer-size">~50MB</span>
                        </div>
                      </div>
                      <div class="total-size optimized">总计: ~80MB</div>
                      <div class="savings">节省 68% 空间！</div>
                    </div>
                  </div>

                  <div class="jlink-commands">
                    <h4>jlink 使用示例</h4>
                    <div class="command-example">
                      <div class="command-block">
                        <code
                          >jlink --add-modules java.base,java.sql,my.app --output my-runtime</code
                        >
                      </div>
                      <p class="command-explanation">
                        创建一个只包含必要模块的自定义运行时，大大减少部署体积
                      </p>
                    </div>

                    <div class="jlink-benefits">
                      <h5>jlink 的优势</h5>
                      <div class="benefits-grid">
                        <div class="benefit-item">
                          <span class="benefit-icon">📦</span>
                          <div>
                            <strong>体积优化</strong>
                            <small>只包含必要模块</small>
                          </div>
                        </div>
                        <div class="benefit-item">
                          <span class="benefit-icon">🔒</span>
                          <div>
                            <strong>安全性提升</strong>
                            <small>减少攻击面</small>
                          </div>
                        </div>
                        <div class="benefit-item">
                          <span class="benefit-icon">🚀</span>
                          <div>
                            <strong>启动加速</strong>
                            <small>更少的类加载</small>
                          </div>
                        </div>
                        <div class="benefit-item">
                          <span class="benefit-icon">🌱</span>
                          <div>
                            <strong>绿色部署</strong>
                            <small>无需预装Java</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import ModuleSystemAnimation from '@/components/ModuleSystemAnimation.vue'
import ModuleCodePlayground from '@/components/ModuleCodePlayground.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: '模块化背景',
    description: '理解JAR Hell问题和模块系统的解决方案',
  },
  {
    title: '模块基本语法',
    description: '掌握module-info.java的核心语法',
  },
  {
    title: '模块加载与类型',
    description: '了解四种模块类型和加载机制',
  },
  {
    title: '构建和运行',
    description: '学习模块化应用的编译和运行',
  },
  {
    title: '架构设计',
    description: '掌握模块化设计模式和最佳实践',
  },
  {
    title: 'jlink工具',
    description: '使用jlink创建精简运行时',
  },
]

// 数据定义
const moduleBackgroundData = {
  keyPoints: [
    'JAR Hell：版本冲突和依赖管理混乱',
    'Jigsaw项目：Java平台模块系统的官方名称',
    '强封装：保护内部API，防止滥用',
    '可靠配置：启动时验证依赖关系',
  ],
}

const moduleInfoData = {
  keyPoints: [
    'module声明：定义模块名称',
    'requires：声明依赖的模块',
    'exports：导出公开的包',
    'requires transitive：传递性依赖',
    'opens：为反射开放包',
  ],
}

const moduleTypesData = {
  keyPoints: [
    '平台模块：JDK内置模块，如java.base',
    '应用模块：有module-info.java的用户模块',
    '自动模块：模块路径上的传统JAR',
    '未命名模块：类路径上的所有JAR',
  ],
}

const buildRunData = {
  keyPoints: [
    '--module-path：指定模块路径',
    '-m：指定主模块和主类',
    '--add-opens：为反射开放包',
    '--add-exports：导出未公开的包',
  ],
}

const architectureData = {
  keyPoints: [
    '分裂包：同一包分散在多个模块中，严格禁止',
    '多版本JAR：同一JAR支持多个Java版本',
    '迁移策略：从classpath到模块的渐进式迁移',
    '设计原则：明确的API边界和依赖关系',
  ],
}

const jlinkData = {
  keyPoints: [
    '自定义运行时：只包含必要的模块',
    '体积优化：从200MB减少到30MB',
    '安全性：减少攻击面',
    '绿色部署：无需预装Java环境',
  ],
}

const moduleInfoExamples = [
  {
    title: '基本模块声明',
    code: `module wgjd.discovery {
    // 依赖声明
    requires java.instrument;
    requires java.sql;

    // API导出
    exports wgjd.discovery;

    // 限定导出
    exports wgjd.discovery.internal to com.another.module;
}`,
    explanation: '展示了模块的基本语法结构',
  },
  {
    title: '传递性依赖',
    code: `module my.library {
    // 传递性依赖：依赖我的模块也会自动依赖java.sql
    requires transitive java.sql;

    // 普通依赖
    requires java.base;

    // 导出API
    exports com.mylib.api;
}`,
    explanation: 'requires transitive会将依赖传递给下游模块',
  },
  {
    title: '反射开放',
    code: `open module my.framework {
    requires java.base;

    // 整个模块对反射开放
    exports com.framework.api;
}

// 或者选择性开放
module my.app {
    requires java.base;

    // 只对特定包开放反射
    opens com.app.internal to spring.core;

    exports com.app.api;
}`,
    explanation: 'open关键字允许反射访问模块内部',
  },
]

// 方法
const scrollToTopic = (index: number) => {
  currentTopic.value = index
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('Interaction:', type)
}

const handleCodeRun = (data: any) => {
  console.log('Code run:', data)
}
</script>

<style scoped>
/* 基础样式 */
.java-chapter2 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 0;
  text-align: center;
}

.chapter-title {
  font-size: 2.5rem;
  margin: 0 0 1rem 0;
  font-weight: 700;
}

.chapter-subtitle {
  font-size: 1.2rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  height: 8px;
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.progress-fill {
  background: white;
  height: 100%;
  border-radius: 25px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -30px;
  right: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

.sidebar {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.outline {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.outline h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
}

.outline-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.5rem;
}

.outline-item:hover {
  background: #f8f9fa;
}

.outline-item.active {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.outline-number {
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.outline-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.outline-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tool-button {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.tool-button:hover {
  background: #f8f9fa;
  border-color: #667eea;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.topic-section {
  margin-bottom: 2rem;
}

.topic-section:last-child {
  margin-bottom: 0;
}

/* JAR Hell 演示样式 */
.jar-hell-demo {
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 2rem 0;
}

.problem-showcase {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: center;
  margin-top: 1.5rem;
}

.before-modules,
.after-modules {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.before-modules h4,
.after-modules h4 {
  margin: 0 0 1rem 0;
  color: #333;
  text-align: center;
}

.jar-conflicts {
  margin-bottom: 1rem;
}

.jar-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin: 0.5rem 0;
  background: #e8f5e8;
  border: 2px solid #4caf50;
  border-radius: 6px;
}

.jar-item.conflict {
  background: #ffebee;
  border-color: #f44336;
}

.conflict-badge {
  background: #f44336;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.arrow-separator {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

.module-graph {
  margin-bottom: 1rem;
}

.module-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin: 0.5rem 0;
  border-radius: 6px;
  border: 2px solid;
}

.module-item.platform {
  background: #e3f2fd;
  border-color: #2196f3;
}

.module-item.application {
  background: #e8f5e8;
  border-color: #4caf50;
}

.module-item.automatic {
  background: #fff3e0;
  border-color: #ff9800;
}

.module-type {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
}

.problem-description,
.solution-description {
  margin-top: 1rem;
}

.problem-description p,
.solution-description p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

/* 模块类型详解样式 */
.module-types-detail {
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 2rem 0;
}

.types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.type-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
}

.type-card.platform {
  border-left-color: #2196f3;
}

.type-card.application {
  border-left-color: #4caf50;
}

.type-card.automatic {
  border-left-color: #ff9800;
}

.type-card.unnamed {
  border-left-color: #9e9e9e;
}

.type-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.type-icon {
  font-size: 1.5rem;
}

.type-header h4 {
  margin: 0;
  flex: 1;
  color: #333;
}

.type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  background: #e9ecef;
  color: #495057;
}

.type-content p {
  margin: 0.5rem 0;
  color: #666;
  line-height: 1.6;
}

.type-examples {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #555;
  line-height: 1.6;
}

.type-examples code {
  background: #e9ecef;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

@media (max-width: 768px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .chapter-title {
    font-size: 2rem;
  }

  .problem-showcase {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .arrow-separator {
    transform: rotate(90deg);
  }

  .types-grid {
    grid-template-columns: 1fr;
  }
}

/* 命令行工具样式 */
.command-showcase {
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 2rem 0;
}

.command-sections {
  margin-top: 1.5rem;
}

.command-section {
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.command-section h4 {
  margin: 0 0 1rem 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.command-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  overflow-x: auto;
  margin-bottom: 1rem;
}

.command-block code {
  color: #e2e8f0;
  background: none;
}

.command-explanation {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0;
}

.command-explanation code {
  background: #e9ecef;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  color: #495057;
}

/* 架构模式样式 */
.architecture-patterns {
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 2rem 0;
}

.pattern-detail {
  margin-bottom: 3rem;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.pattern-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.pattern-icon {
  font-size: 2rem;
}

.pattern-header h4 {
  margin: 0;
  flex: 1;
  font-size: 1.3rem;
}

.severity-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.severity-badge.danger {
  background: rgba(244, 67, 54, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.severity-badge.success {
  background: rgba(76, 175, 80, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.pattern-content {
  padding: 2rem;
}

/* 分裂包演示样式 */
.split-package-example {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 1.5rem 0;
  justify-content: center;
}

.module-box {
  background: #e3f2fd;
  border: 2px solid #2196f3;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  min-width: 150px;
}

.module-box h5 {
  margin: 0 0 0.5rem 0;
  color: #1976d2;
}

.package-item {
  background: #fff;
  padding: 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #333;
}

.conflict-indicator {
  font-size: 2rem;
  color: #f44336;
  font-weight: bold;
}

.problem-explanation {
  background: #ffebee;
  border-left: 4px solid #f44336;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 4px;
}

.solution-box {
  background: #e8f5e8;
  border-left: 4px solid #4caf50;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 4px;
}

.solution-box h6 {
  margin: 0 0 0.5rem 0;
  color: #2e7d32;
}

.solution-box ul {
  margin: 0.5rem 0 0 1rem;
  color: #388e3c;
}

/* 多版本JAR样式 */
.mr-jar-structure {
  margin-top: 1rem;
}

.file-tree {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  margin: 1rem 0;
}

.tree-item {
  margin: 0.25rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tree-item.level-1 {
  margin-left: 1rem;
}

.tree-item.level-2 {
  margin-left: 2rem;
}

.tree-item.level-3 {
  margin-left: 3rem;
}

.tree-item.level-4 {
  margin-left: 4rem;
}

.version-tag {
  background: #4caf50;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  margin-left: auto;
}

.mr-jar-explanation {
  color: #666;
  font-style: italic;
  line-height: 1.6;
  margin-top: 1rem;
}

/* jlink展示样式 */
.jlink-showcase {
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 2rem 0;
}

.jlink-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.traditional-deployment,
.jlink-deployment {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.traditional-deployment h4,
.jlink-deployment h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.deployment-stack {
  margin: 1rem 0;
}

.layer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin: 0.5rem 0;
  border-radius: 6px;
  font-weight: 500;
}

.jre-layer {
  background: #ffcdd2;
  border: 2px solid #f44336;
}

.custom-runtime {
  background: #c8e6c9;
  border: 2px solid #4caf50;
}

.app-layer {
  background: #e1f5fe;
  border: 2px solid #03a9f4;
}

.total-size {
  font-size: 1.2rem;
  font-weight: 600;
  padding: 0.75rem;
  border-radius: 6px;
  margin-top: 1rem;
}

.total-size.traditional {
  background: #ffebee;
  color: #c62828;
}

.total-size.optimized {
  background: #e8f5e8;
  color: #2e7d32;
}

.savings {
  background: #4caf50;
  color: white;
  padding: 0.5rem;
  border-radius: 6px;
  margin-top: 0.5rem;
  font-weight: 600;
}

.jlink-commands {
  margin-top: 2rem;
}

.command-example {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

.jlink-benefits {
  margin-top: 2rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
  font-size: 1.5rem;
}

.benefit-item strong {
  display: block;
  color: #333;
  margin-bottom: 0.25rem;
}

.benefit-item small {
  color: #666;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .jlink-comparison {
    grid-template-columns: 1fr;
  }

  .split-package-example {
    flex-direction: column;
  }

  .conflict-indicator {
    transform: rotate(90deg);
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }
}
</style>
