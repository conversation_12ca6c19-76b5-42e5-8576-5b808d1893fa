<template>
  <div class="process-animation">
    <h3 class="animation-title">{{ title }}</h3>

    <div class="process-flow">
      <div
        v-for="(step, index) in steps"
        :key="index"
        class="process-step"
        :class="{
          active: currentStep >= index,
          completed: currentStep > index,
          current: currentStep === index,
        }"
      >
        <div class="step-icon">{{ step.icon }}</div>
        <div class="step-content">
          <h4 class="step-title">{{ step.title }}</h4>
          <p class="step-description">{{ step.description }}</p>
          <div v-if="step.details" class="step-details">
            {{ step.details }}
          </div>
        </div>

        <!-- 连接线 -->
        <div v-if="index < steps.length - 1" class="connector">
          <div class="connector-line" :class="{ active: currentStep > index }"></div>
          <div class="connector-arrow">→</div>
        </div>
      </div>
    </div>

    <!-- 控制按钮 -->
    <div class="controls">
      <button @click="startAnimation" class="control-button">🎬 开始演示</button>
      <button @click="resetAnimation" class="control-button">🔄 重置</button>
      <button @click="prevStep" :disabled="currentStep <= 0" class="control-button">
        ⏮️ 上一步
      </button>
      <button @click="nextStep" :disabled="currentStep >= steps.length - 1" class="control-button">
        ⏭️ 下一步
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface ProcessStep {
  title: string
  description: string
  icon: string
  details?: string
}

const props = defineProps<{
  steps: ProcessStep[]
  title: string
}>()

const currentStep = ref(-1)

const startAnimation = () => {
  currentStep.value = -1
  nextStep()
}

const nextStep = () => {
  if (currentStep.value < props.steps.length - 1) {
    currentStep.value++
    setTimeout(() => {
      if (currentStep.value < props.steps.length - 1) {
        nextStep()
      }
    }, 2000)
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const resetAnimation = () => {
  currentStep.value = -1
}
</script>

<style scoped>
.process-animation {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.animation-title {
  text-align: center;
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 2rem;
}

.process-flow {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 2rem;
}

.process-step {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #e9ecef;
  opacity: 0.5;
  transition: all 0.3s ease;
  position: relative;
}

.process-step.active {
  opacity: 1;
  border-left-color: #667eea;
  background: #f0f4ff;
}

.process-step.current {
  background: #e8f5e8;
  border-left-color: #4caf50;
  transform: scale(1.02);
}

.process-step.completed {
  background: #f3e5f5;
  border-left-color: #9c27b0;
}

.step-icon {
  font-size: 2rem;
  margin-right: 1.5rem;
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-content {
  flex: 1;
}

.step-title {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.step-description {
  margin: 0 0 0.5rem 0;
  color: #666;
  line-height: 1.6;
}

.step-details {
  font-size: 0.9rem;
  color: #888;
  font-style: italic;
}

.connector {
  position: absolute;
  right: -1rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.connector-line {
  width: 2rem;
  height: 2px;
  background: #e9ecef;
  transition: background 0.3s ease;
}

.connector-line.active {
  background: #667eea;
}

.connector-arrow {
  color: #e9ecef;
  font-size: 1.2rem;
  transition: color 0.3s ease;
}

.process-step.active + .process-step .connector-arrow {
  color: #667eea;
}

.controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.control-button {
  padding: 0.75rem 1.5rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.control-button:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-2px);
}

.control-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

@media (max-width: 768px) {
  .process-animation {
    padding: 1rem;
  }

  .process-step {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .step-icon {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .connector {
    display: none;
  }

  .controls {
    flex-direction: column;
    align-items: center;
  }

  .control-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
