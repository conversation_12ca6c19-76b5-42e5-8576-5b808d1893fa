<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
import { ref } from 'vue'

const showChapterMenu = ref(false)

const chapters = [
  { id: 1, title: '第1章：Java简介', path: '/chapter1' },
  { id: 2, title: '第2章：Java基础', path: '/chapter2' },
  { id: 3, title: '第3章：Java 17', path: '/java17' },
  { id: 4, title: '第4章：异步与并发', path: '/chapter4' },
  { id: 5, title: '第5章：性能优化', path: '/chapter5' },
]

const toggleChapterMenu = () => {
  showChapterMenu.value = !showChapterMenu.value
}

const closeChapterMenu = () => {
  showChapterMenu.value = false
}
</script>

<template>
  <header>
    <div class="header-content">
      <div class="book-info" @click="toggleChapterMenu">
        <h1 class="book-title">📖 The Well-Grounded Java Developer</h1>
        <span class="book-subtitle">Second Edition</span>
        <span class="dropdown-icon" :class="{ rotated: showChapterMenu }">▼</span>

        <!-- 章节下拉菜单 -->
        <div v-if="showChapterMenu" class="chapter-dropdown" @click.stop>
          <div class="dropdown-header">章节目录</div>
          <RouterLink
            v-for="chapter in chapters"
            :key="chapter.id"
            :to="chapter.path"
            class="chapter-link"
            @click="closeChapterMenu"
          >
            {{ chapter.title }}
          </RouterLink>
        </div>
      </div>

      <nav class="main-nav">
        <RouterLink to="/">首页</RouterLink>
        <RouterLink to="/java17">Java 17</RouterLink>
        <RouterLink to="/about">关于</RouterLink>
      </nav>
    </div>

    <!-- 遮罩层 -->
    <div v-if="showChapterMenu" class="backdrop" @click="closeChapterMenu"></div>
  </header>

  <main>
    <RouterView />
  </main>
</template>

<style scoped>
header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.book-info {
  position: relative;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.book-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.book-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.book-subtitle {
  font-size: 0.9rem;
  opacity: 0.8;
  font-style: italic;
  margin-top: 0.2rem;
}

.dropdown-icon {
  font-size: 0.8rem;
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
  display: inline-block;
}

.dropdown-icon.rotated {
  transform: rotate(180deg);
}

.chapter-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  min-width: 300px;
  z-index: 1001;
  animation: dropdownSlide 0.3s ease-out;
}

@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-header {
  padding: 0.75rem 1rem;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.chapter-link {
  display: block;
  padding: 0.75rem 1rem;
  color: #555;
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.chapter-link:hover {
  background: #f8f9fa;
  color: #667eea;
  border-left-color: #667eea;
}

.chapter-link.router-link-active {
  background: #e3f2fd;
  color: #1976d2;
  border-left-color: #1976d2;
  font-weight: 600;
}

.main-nav {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.main-nav a {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  transition: all 0.3s ease;
  font-weight: 500;
  opacity: 0.9;
}

.main-nav a:hover {
  background: rgba(255, 255, 255, 0.2);
  opacity: 1;
  transform: translateY(-2px);
}

.main-nav a.router-link-exact-active {
  background: rgba(255, 255, 255, 0.25);
  opacity: 1;
  font-weight: 600;
}

.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 999;
}

main {
  min-height: calc(100vh - 100px);
  padding: 2rem 0;
  background: #f8f9fa;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .book-title {
    font-size: 1.5rem;
  }

  .main-nav {
    gap: 1rem;
  }

  .main-nav a {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  .chapter-dropdown {
    left: -1rem;
    right: -1rem;
    min-width: auto;
  }
}
</style>
